#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QAction>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QMessageBox>
#include <QtCore/QTimer>
#include <QtCore/QSettings>
#include <memory>

// Forward declarations for camera managers
#ifdef OPENCV_AVAILABLE
class OpenCVCameraManager;
typedef OpenCVCameraManager CameraManagerType;
#else
class CameraManager;
typedef CameraManager CameraManagerType;
#endif

class ConfigManager;

class AddCredentialDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddCredentialDialog(QWidget *parent = nullptr);
    QString getUsername() const;
    QString getPassword() const;

private:
    QLineEdit *m_usernameEdit;
    QLineEdit *m_passwordEdit;
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onConnectClicked();
    void onDisconnectClicked();
    void onAddCredentialClicked();
    void onRemoveCredentialClicked();
    void onCredentialSelectionChanged();
    void onIpAddressChanged();
    void updateConnectionStatus();
    void onVideoFrameReceived();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void loadSettings();
    void saveSettings();
    void updateCredentialsList();
    void updateButtonStates();
    void showErrorMessage(const QString &message);
    void showStatusMessage(const QString &message, int timeout = 3000);

    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    
    // Video display area
    QWidget *m_videoWidget;
    QLabel *m_videoLabel;
    
    // Control panel
    QWidget *m_controlPanel;
    QGroupBox *m_connectionGroup;
    QLineEdit *m_ipAddressEdit;
    QComboBox *m_credentialsCombo;
    QPushButton *m_connectButton;
    QPushButton *m_disconnectButton;
    
    QGroupBox *m_credentialsGroup;
    QListWidget *m_credentialsList;
    QPushButton *m_addCredentialButton;
    QPushButton *m_removeCredentialButton;
    
    // Status
    QLabel *m_statusLabel;
    QTimer *m_statusTimer;
    
    // Managers
    std::unique_ptr<CameraManagerType> m_cameraManager;
    std::unique_ptr<ConfigManager> m_configManager;
    
    // State
    bool m_isConnected;
    QString m_currentIpAddress;
};

#endif // MAINWINDOW_H
