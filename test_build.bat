@echo off
echo Testing Hikvision Camera Viewer Build...
echo.

REM Check if Qt is available
where qmake >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Warning: Qt qmake not found in PATH
    echo Please ensure Qt is properly installed and added to PATH
    echo.
)

REM Check if CMake is available
where cmake >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: CMake not found in PATH
    echo Please install CMake and add it to PATH
    pause
    exit /b 1
)

REM Check project structure
echo Checking project structure...
if not exist "CMakeLists.txt" (
    echo Error: CMakeLists.txt not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "src" (
    echo Error: src directory not found
    pause
    exit /b 1
)

if not exist "include" (
    echo Error: include directory not found
    pause
    exit /b 1
)

echo Project structure OK
echo.

REM Check for Hikvision SDK
echo Checking Hikvision SDK...
if exist "lib\HCNetSDK.lib" (
    echo ✓ HCNetSDK.lib found
) else (
    echo ✗ HCNetSDK.lib not found
)

if exist "lib\PlayCtrl.lib" (
    echo ✓ PlayCtrl.lib found
) else (
    echo ✗ PlayCtrl.lib not found
)

if exist "lib\dll\HCNetSDK.dll" (
    echo ✓ HCNetSDK.dll found
) else (
    echo ✗ HCNetSDK.dll not found
)

if exist "lib\dll\PlayCtrl.dll" (
    echo ✓ PlayCtrl.dll found
) else (
    echo ✗ PlayCtrl.dll not found
)

if not exist "lib\HCNetSDK.lib" (
    echo.
    echo Warning: Hikvision SDK files not found
    echo The application will compile but camera functionality will be limited
    echo Please refer to SDK_SETUP.md for installation instructions
    echo.
)

REM Attempt to build
echo Starting build process...
call build.bat

echo.
echo Test completed!
pause
