# 海康威视SDK安装指南

本文档详细说明如何获取和安装海康威视网络SDK以支持本项目。

## 1. 下载SDK

### 官方渠道（推荐方式）

#### 方式1：海康威视开放平台
1. 访问海康威视开放平台：https://open.hikvision.com/
2. 注册并登录账户
3. 进入"下载中心" -> "SDK下载"
4. 选择"设备网络SDK" -> "Windows版本"
5. 下载最新版本的SDK包

#### 方式2：海康威视官网技术支持
1. 访问：https://www.hikvision.com/cn/support/
2. 点击"下载中心"
3. 在搜索框中输入"网络SDK"或"Device Network SDK"
4. 选择适合的Windows版本下载

#### 方式3：直接链接（如果可用）
- 设备网络SDK下载页面：https://www.hikvision.com/cn/support/download/sdk/
- 注意：可能需要注册账户才能下载

### 替代获取方式

#### 方式4：通过设备厂商
如果您购买了海康威视设备：
1. 联系设备供应商或经销商
2. 他们通常可以提供SDK文件
3. 或者提供正确的下载链接

#### 方式5：技术支持
1. 拨打海康威视技术支持热线：400-700-5998
2. 说明需要下载设备网络SDK
3. 他们会提供正确的下载方式

#### 方式6：GitHub或开源社区
一些开发者可能在GitHub上分享了SDK文件（仅供学习使用）：
- 搜索关键词："hikvision sdk"、"HCNetSDK"
- 注意：确保来源可靠，避免恶意软件

### 所需文件
下载的SDK包通常包含以下重要文件：
- `HCNetSDK.dll` - 主要的网络SDK动态库
- `HCNetSDK.lib` - 链接库文件
- `PlayCtrl.dll` - 播放控制动态库
- `PlayCtrl.lib` - 播放控制链接库
- 其他依赖的DLL文件

## 2. 安装SDK文件

### 创建lib目录结构
在项目根目录下创建以下目录结构：
```
lib/
├── HCNetSDK.lib
├── PlayCtrl.lib
└── dll/
    ├── HCNetSDK.dll
    ├── PlayCtrl.dll
    ├── AudioRender.dll
    ├── HCCore.dll
    ├── HCAlarm.dll
    ├── HCGeneralCfgMgr.dll
    ├── HCPreview.dll
    ├── HCVoiceTalk.dll
    ├── libeay32.dll
    ├── ssleay32.dll
    └── ... (其他依赖DLL)
```

### 复制文件步骤
1. 将 `HCNetSDK.lib` 和 `PlayCtrl.lib` 复制到 `lib/` 目录
2. 将所有DLL文件复制到 `lib/dll/` 目录
3. 确保包含所有依赖的DLL文件

## 3. 常见的SDK文件列表

以下是通常需要的DLL文件列表（版本可能有所不同）：

### 核心文件
- `HCNetSDK.dll` - 主SDK
- `PlayCtrl.dll` - 播放控制
- `HCCore.dll` - 核心库

### 功能模块
- `HCAlarm.dll` - 报警处理
- `HCGeneralCfgMgr.dll` - 配置管理
- `HCPreview.dll` - 预览功能
- `HCVoiceTalk.dll` - 语音对讲

### 编解码
- `AudioRender.dll` - 音频渲染
- `DecodeCard.dll` - 解码卡支持

### 安全库
- `libeay32.dll` - OpenSSL加密库
- `ssleay32.dll` - SSL支持

### 其他依赖
- `msvcr120.dll` - Visual C++ 运行时（如果需要）
- `msvcp120.dll` - Visual C++ 运行时（如果需要）

## 4. 验证安装

### 检查文件完整性
运行以下PowerShell命令检查文件是否存在：

```powershell
# 检查lib文件
Test-Path "lib\HCNetSDK.lib"
Test-Path "lib\PlayCtrl.lib"

# 检查主要DLL文件
Test-Path "lib\dll\HCNetSDK.dll"
Test-Path "lib\dll\PlayCtrl.dll"
```

### 构建测试
1. 运行 `build.bat` 进行构建
2. 如果构建成功，说明SDK文件配置正确
3. 如果出现链接错误，检查lib文件路径
4. 如果运行时出错，检查DLL文件是否完整

## 5. 故障排除

### 常见问题

#### 1. 链接错误
```
error LNK2019: unresolved external symbol
```
**解决方案**: 检查 `HCNetSDK.lib` 和 `PlayCtrl.lib` 是否在正确位置

#### 2. 运行时DLL缺失
```
The program can't start because xxx.dll is missing
```
**解决方案**: 
- 检查 `lib/dll/` 目录中是否包含所需DLL
- 运行 `build.bat` 确保DLL被复制到输出目录

#### 3. 初始化失败
```
Failed to initialize Hikvision SDK
```
**解决方案**:
- 确认所有依赖DLL都已安装
- 检查SDK版本兼容性
- 以管理员权限运行程序

### 调试技巧

1. **使用Dependency Walker**
   - 下载并使用Dependency Walker工具
   - 检查程序的DLL依赖关系
   - 找出缺失的依赖文件

2. **检查事件日志**
   - 打开Windows事件查看器
   - 查看应用程序日志中的错误信息

3. **使用Process Monitor**
   - 监控程序运行时的文件访问
   - 找出程序尝试加载但找不到的文件

## 6. 版本兼容性

### 推荐版本
- 海康威视网络SDK 6.1.9.47 或更高版本
- 支持Windows 10/11 64位系统

### 注意事项
- 不同版本的SDK可能有不同的API
- 建议使用最新稳定版本
- 某些旧版本可能不支持新的摄像头型号

## 7. 许可和法律

- 海康威视SDK受其许可协议约束
- 仅用于合法的监控和安防目的
- 遵守当地法律法规关于视频监控的规定
- 不得用于非法监控或侵犯他人隐私

## 8. 技术支持

如果遇到SDK相关问题：
1. 查阅海康威视官方文档
2. 联系海康威视技术支持
3. 参考官方示例代码
4. 查看SDK更新日志

---

**重要提示**: 本项目仅为示例代码，实际使用时请确保遵守所有相关法律法规和许可协议。
