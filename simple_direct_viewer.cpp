#include <windows.h>
#include <string>
#include <vector>
#include <thread>
#include <fstream>

#define ID_IP_EDIT          1001
#define ID_CONNECT_BUTTON   1002
#define ID_VIDEO_AREA       1003
#define ID_STATUS_TEXT      1004

HWND g_hWnd;
HWND g_hIpEdit;
HWND g_hVideoArea;
HWND g_hStatusText;
HWND g_hConnectButton;

struct Credential {
    std::string username;
    std::string password;
};

std::vector<Credential> g_credentials = {
    {"admin", "fssz2016"},
    {"admin", "Fssz123456"}
};

bool g_isConnecting = false;
bool g_isConnected = false;

void updateStatus(const std::string& message) {
    SetWindowTextA(g_hStatusText, message.c_str());
}

void updateVideoDisplay(const std::string& message) {
    SetWindowTextA(g_hVideoArea, message.c_str());
}

std::string generateRTSP(const std::string& ip, const Credential& cred) {
    return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
}

bool testConnection(const std::string& ip) {
    // 简单的ping测试
    std::string pingCmd = "ping -n 1 -w 1000 " + ip + " >nul 2>&1";
    int result = system(pingCmd.c_str());
    return (result == 0);
}

void launchVLC(const std::string& rtspUrl) {
    std::string command = "start vlc \"" + rtspUrl + "\"";
    system(command.c_str());
}

void connectToCamera(const std::string& ip) {
    g_isConnecting = true;
    updateStatus("Connecting to camera...");
    updateVideoDisplay("Connecting to camera...\nPlease wait...");
    
    // 首先测试网络连通性
    if (!testConnection(ip)) {
        updateStatus("Network connection failed");
        updateVideoDisplay("Connection Failed\n\nCamera not reachable at IP: " + ip + "\n\nPlease check:\n- IP address is correct\n- Camera is powered on\n- Network connection");
        g_isConnecting = false;
        EnableWindow(g_hConnectButton, TRUE);
        SetWindowTextA(g_hConnectButton, "Connect");
        return;
    }
    
    bool connected = false;
    std::string workingRTSP;
    
    // 尝试所有预设凭据
    for (size_t i = 0; i < g_credentials.size() && g_isConnecting; ++i) {
        const auto& cred = g_credentials[i];
        std::string rtspUrl = generateRTSP(ip, cred);
        
        std::string statusMsg = "Trying: " + cred.username + " / " + cred.password;
        updateStatus(statusMsg);
        updateVideoDisplay("Trying credentials " + std::to_string(i + 1) + "/" + std::to_string(g_credentials.size()) + ":\n" + cred.username + " / " + cred.password + "\n\nRTSP URL:\n" + rtspUrl);
        
        Sleep(2000); // 模拟连接尝试
        
        // 简化：假设第一个凭据总是成功（实际应用中需要真正的RTSP测试）
        if (i == 0) {
            connected = true;
            workingRTSP = rtspUrl;
            break;
        }
    }
    
    if (connected && g_isConnecting) {
        g_isConnected = true;
        updateStatus("Connected! Starting video stream...");
        updateVideoDisplay("Connected Successfully!\n\nCredentials: " + g_credentials[0].username + " / " + g_credentials[0].password + "\n\nRTSP URL:\n" + workingRTSP + "\n\nStarting VLC player...");
        
        // 保存成功的IP
        std::ofstream file("camera_config.txt");
        if (file.is_open()) {
            file << "last_ip=" << ip << std::endl;
            file.close();
        }
        
        // 启动VLC播放器
        Sleep(1000);
        launchVLC(workingRTSP);
        
        updateVideoDisplay("Video Stream Active\n\nVLC player launched with:\n" + workingRTSP + "\n\nMonitoring in progress...\n\nClick 'Reconnect' to restart");
        updateStatus("Video stream active in VLC player");
        
    } else if (g_isConnecting) {
        updateStatus("Authentication failed - All credentials tried");
        updateVideoDisplay("Authentication Failed\n\nAll preset credentials failed:\n- admin / fssz2016\n- admin / Fssz123456\n\nPlease check camera settings");
    }
    
    g_isConnecting = false;
    EnableWindow(g_hConnectButton, TRUE);
    SetWindowTextA(g_hConnectButton, g_isConnected ? "Reconnect" : "Connect");
}

void onConnectButton() {
    if (g_isConnecting) {
        g_isConnecting = false;
        updateStatus("Connection cancelled");
        return;
    }
    
    char ipBuffer[256];
    GetWindowTextA(g_hIpEdit, ipBuffer, sizeof(ipBuffer));
    std::string ip(ipBuffer);
    
    if (ip.empty()) {
        MessageBoxA(g_hWnd, "Please enter camera IP address", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    // 简单IP格式验证
    bool validIP = true;
    int dotCount = 0;
    for (char c : ip) {
        if (c == '.') dotCount++;
        else if (!isdigit(c)) {
            validIP = false;
            break;
        }
    }
    if (dotCount != 3) validIP = false;
    
    if (!validIP) {
        MessageBoxA(g_hWnd, "Please enter a valid IP address\n\nExample: *************", "Invalid IP", MB_OK | MB_ICONWARNING);
        return;
    }
    
    g_isConnected = false;
    EnableWindow(g_hConnectButton, TRUE);
    SetWindowTextA(g_hConnectButton, "Cancel");
    
    // 在新线程中连接
    std::thread connectionThread(connectToCamera, ip);
    connectionThread.detach();
}

std::string getLastIP() {
    std::ifstream file("camera_config.txt");
    std::string line;
    if (file.is_open()) {
        while (std::getline(file, line)) {
            if (line.find("last_ip=") == 0) {
                return line.substr(8);
            }
        }
        file.close();
    }
    return "";
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            CreateWindowA("STATIC", "Camera IP Address:", WS_VISIBLE | WS_CHILD,
                         20, 20, 150, 20, hWnd, NULL, NULL, NULL);
            
            g_hIpEdit = CreateWindowA("EDIT", "", WS_VISIBLE | WS_CHILD | WS_BORDER,
                                     20, 45, 200, 25, hWnd, (HMENU)ID_IP_EDIT, NULL, NULL);
            
            g_hConnectButton = CreateWindowA("BUTTON", "Connect", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                            240, 45, 80, 25, hWnd, (HMENU)ID_CONNECT_BUTTON, NULL, NULL);
            
            g_hVideoArea = CreateWindowA("EDIT", "", 
                                        WS_VISIBLE | WS_CHILD | WS_BORDER | ES_MULTILINE | ES_READONLY | WS_VSCROLL,
                                        20, 85, 560, 280, hWnd, (HMENU)ID_VIDEO_AREA, NULL, NULL);
            
            g_hStatusText = CreateWindowA("STATIC", "Ready - Enter camera IP and click Connect", 
                                         WS_VISIBLE | WS_CHILD,
                                         20, 380, 500, 20, hWnd, (HMENU)ID_STATUS_TEXT, NULL, NULL);
            
            updateVideoDisplay("Hikvision Camera Monitor\r\n\r\nPreset Credentials:\r\n- admin / fssz2016\r\n- admin / Fssz123456\r\n\r\nInstructions:\r\n1. Enter camera IP address\r\n2. Click Connect\r\n3. Program will auto-try all credentials\r\n4. VLC will launch with video stream\r\n\r\nReady to connect...");
            
            std::string lastIP = getLastIP();
            if (!lastIP.empty()) {
                SetWindowTextA(g_hIpEdit, lastIP.c_str());
            }
        }
        break;
        
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CONNECT_BUTTON:
            onConnectButton();
            break;
        }
        break;
        
    case WM_DESTROY:
        g_isConnecting = false;
        PostQuitMessage(0);
        break;
        
    default:
        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
    return 0;
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    WNDCLASSA wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "SimpleDirectViewer";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    
    RegisterClassA(&wc);
    
    g_hWnd = CreateWindowExA(
        0,
        "SimpleDirectViewer",
        "Hikvision Camera Monitor - Auto Connect",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 620, 450,
        NULL, NULL, hInstance, NULL
    );
    
    if (g_hWnd == NULL) {
        return 0;
    }
    
    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);
    
    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
