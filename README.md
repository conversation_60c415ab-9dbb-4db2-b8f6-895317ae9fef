# Hikvision Camera Viewer

一个用于显示海康威视网络摄像头监控画面的C++应用程序，基于Qt框架开发。

## 功能特性

- 🎥 实时显示海康威视网络摄像头画面
- 🔐 支持多组用户名/密码管理
- 💾 自动保存最近使用的IP地址
- 🖥️ 支持窗口最大化和全屏显示
- 🔄 自动重连功能
- ⚙️ 配置信息本地保存

## 系统要求

- Windows 10/11 (64位)
- Qt 6.2 或更高版本
- CMake 3.16 或更高版本
- Visual Studio 2019 或更高版本 (或其他C++17兼容编译器)
- 海康威视网络SDK

## 安装依赖

### 1. 安装Qt

从 [Qt官网](https://www.qt.io/download) 下载并安装Qt 6.2或更高版本。

### 2. 获取海康威视SDK

1. 访问海康威视官网下载网络SDK
2. 解压SDK文件
3. 将以下文件复制到项目的 `lib` 目录：
   - `HCNetSDK.lib`
   - `PlayCtrl.lib`
4. 将以下DLL文件复制到 `lib/dll` 目录：
   - `HCNetSDK.dll`
   - `PlayCtrl.dll`
   - 以及其他相关的依赖DLL文件

### 3. 项目目录结构

```
HikvisionViewer/
├── CMakeLists.txt
├── README.md
├── include/
│   ├── MainWindow.h
│   ├── CameraManager.h
│   └── ConfigManager.h
├── src/
│   ├── main.cpp
│   ├── MainWindow.cpp
│   ├── CameraManager.cpp
│   └── ConfigManager.cpp
└── lib/
    ├── HCNetSDK.lib
    ├── PlayCtrl.lib
    └── dll/
        ├── HCNetSDK.dll
        ├── PlayCtrl.dll
        └── ... (其他DLL文件)
```

## 编译构建

### 使用CMake构建

1. 打开命令提示符或PowerShell
2. 导航到项目根目录
3. 创建构建目录并构建：

```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### 使用Qt Creator

1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择项目根目录的 `CMakeLists.txt` 文件
4. 配置项目并构建

## 使用说明

### 1. 启动程序

运行编译生成的 `HikvisionViewer.exe` 文件。

### 2. 连接摄像头

1. 在"IP Address"输入框中输入摄像头的IP地址
2. 从"Credentials"下拉框中选择用户名/密码组合
3. 点击"Connect"按钮连接摄像头

### 3. 管理凭据

- 点击"Add"按钮添加新的用户名/密码组合
- 选择列表中的凭据后点击"Remove"按钮删除

### 4. 默认凭据

程序预设了以下默认凭据：
- admin / fssz2016
- admin / Fssz123456

## 配置文件

程序会在以下位置保存配置文件：
- Windows: `%APPDATA%/CameraViewer/config.ini`

配置文件包含：
- 窗口大小和位置
- 最近使用的IP地址
- 用户凭据（加密存储）
- 其他应用设置

## 故障排除

### 1. 无法连接摄像头

- 检查IP地址是否正确
- 确认摄像头在网络中可访问
- 验证用户名和密码是否正确
- 检查摄像头端口（默认8000）是否开放

### 2. 无法显示视频

- 确认海康威视SDK文件已正确安装
- 检查DLL文件是否在程序目录中
- 查看状态栏的错误信息

### 3. 编译错误

- 确认Qt版本兼容性
- 检查海康威视SDK路径是否正确
- 验证CMake配置

## 开发说明

### 主要类说明

- `MainWindow`: 主窗口类，负责UI界面和用户交互
- `CameraManager`: 摄像头管理类，负责SDK调用和视频流处理
- `ConfigManager`: 配置管理类，负责设置的保存和加载

### 扩展功能

可以考虑添加的功能：
- 支持多摄像头同时显示
- 录像功能
- 截图功能
- 云台控制
- 报警处理

## 许可证

本项目仅供学习和研究使用。使用海康威视SDK需要遵守其相应的许可协议。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件

---

**注意**: 本程序需要海康威视网络SDK支持，请确保已正确安装SDK文件。
