cmake_minimum_required(VERSION 3.16)
project(SimpleViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 创建简化版摄像头查看器
add_executable(simple_viewer simple_viewer.cpp)

# Windows特定设置
if(WIN32)
    set_target_properties(simple_viewer PROPERTIES
        WIN32_EXECUTABLE FALSE  # 保持控制台程序
    )
endif()

message(STATUS "Simple Camera Viewer configured successfully!")
message(STATUS "This version generates RTSP URLs and can launch VLC player")
