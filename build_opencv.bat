@echo off
echo Building Hikvision Camera Viewer with OpenCV support...
echo.

REM Check if build directory exists
if not exist "build_opencv" (
    echo Creating build directory...
    mkdir build_opencv
)

REM Navigate to build directory
cd build_opencv

REM Use the OpenCV-enabled CMakeLists.txt
copy "..\CMakeLists_OpenCV.txt" "..\CMakeLists.txt" >nul

REM Configure with CMake
echo Configuring project with CMake (OpenCV support)...
cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    echo.
    echo Possible solutions:
    echo 1. Install OpenCV using vcpkg: vcpkg install opencv[contrib]:x64-windows
    echo 2. Or download OpenCV from https://opencv.org/releases/
    echo 3. Or install Hikvision SDK according to SDK_SETUP.md
    echo.
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Executable location: build_opencv\Release\HikvisionViewer.exe
echo.

REM Check which camera manager was used
findstr /C:"OpenCV found" CMakeCache.txt >nul
if %ERRORLEVEL% equ 0 (
    echo Camera Manager: OpenCV ^(supports multiple camera brands^)
    echo RTSP URL format: rtsp://username:password@ip:port/path
    echo Example: rtsp://admin:fssz2016@*************:554/Streaming/Channels/101
) else (
    echo Camera Manager: Hikvision SDK ^(Hikvision cameras only^)
    echo Input format: IP address + credentials selection
    
    REM Copy Hikvision DLLs if available
    if exist "..\lib\dll" (
        echo Copying Hikvision DLL files...
        xcopy "..\lib\dll\*" "Release\" /Y /Q
        if %ERRORLEVEL% neq 0 (
            echo Warning: Failed to copy some DLL files
        ) else (
            echo Hikvision DLL files copied successfully
        )
    ) else (
        echo Warning: Hikvision SDK DLLs not found
    )
)

echo.
echo Build process completed!
echo.
echo Usage Tips:
echo - For OpenCV version: Enter full RTSP URL in the IP address field
echo - For Hikvision SDK version: Enter IP address and select credentials
echo - Check ALTERNATIVE_SOLUTION.md for RTSP URL formats
echo.
pause
