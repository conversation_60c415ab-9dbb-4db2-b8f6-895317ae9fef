# 替代解决方案：使用OpenCV实现网络摄像头查看器

由于海康威视SDK获取可能存在困难，这里提供一个使用OpenCV的替代方案，可以连接大多数支持RTSP协议的网络摄像头。

## 方案优势

- ✅ 无需专门的SDK，使用开源的OpenCV
- ✅ 支持多种品牌的网络摄像头（不仅限于海康威视）
- ✅ 跨平台支持（Windows、Linux、macOS）
- ✅ 更容易获取和安装依赖
- ✅ 社区支持丰富

## 海康威视摄像头RTSP地址格式

海康威视摄像头通常支持以下RTSP地址格式：

### 主码流
```
rtsp://用户名:密码@IP地址:554/Streaming/Channels/101
```

### 子码流
```
rtsp://用户名:密码@IP地址:554/Streaming/Channels/102
```

### 示例
```
rtsp://admin:fssz2016@*************:554/Streaming/Channels/101
rtsp://admin:Fssz123456@*************:554/Streaming/Channels/101
```

## 实现步骤

### 1. 安装OpenCV

#### Windows (使用vcpkg)
```bash
# 安装vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装OpenCV
.\vcpkg install opencv[contrib]:x64-windows
```

#### 或者下载预编译版本
1. 访问：https://opencv.org/releases/
2. 下载Windows版本
3. 解压到合适位置

### 2. 修改CMakeLists.txt

创建新的CMakeLists.txt支持OpenCV：

```cmake
cmake_minimum_required(VERSION 3.16)
project(HikvisionViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)
find_package(OpenCV REQUIRED)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${OpenCV_INCLUDE_DIRS})

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/OpenCVCameraManager.cpp
    src/ConfigManager.cpp
)

# Header files
set(HEADERS
    include/MainWindow.h
    include/OpenCVCameraManager.h
    include/ConfigManager.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME} 
    Qt6::Core 
    Qt6::Widgets 
    Qt6::Network
    ${OpenCV_LIBS}
)

# Windows specific settings
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()
```

### 3. 创建OpenCV版本的摄像头管理器

我将为您创建一个基于OpenCV的摄像头管理器，替代原来的海康威视SDK版本。

## 使用说明

1. **RTSP地址格式**：在IP地址输入框中输入完整的RTSP地址
2. **认证信息**：直接在RTSP地址中包含用户名和密码
3. **连接测试**：程序会自动尝试连接并显示视频流

## 支持的摄像头品牌

- 海康威视 (Hikvision)
- 大华 (Dahua)
- 宇视 (Uniview)
- 华为 (Huawei)
- 以及其他支持RTSP协议的网络摄像头

## 常见RTSP地址格式

### 海康威视
```
rtsp://用户名:密码@IP:554/Streaming/Channels/101
rtsp://用户名:密码@IP:554/h264/ch1/main/av_stream
```

### 大华
```
rtsp://用户名:密码@IP:554/cam/realmonitor?channel=1&subtype=0
```

### 宇视
```
rtsp://用户名:密码@IP:554/video1
```

## 故障排除

### 1. 无法连接摄像头
- 检查RTSP地址格式是否正确
- 确认用户名和密码正确
- 检查网络连接
- 尝试使用VLC播放器测试RTSP地址

### 2. 视频显示问题
- 检查OpenCV是否正确安装
- 确认摄像头支持的编码格式
- 尝试不同的码流（主码流/子码流）

### 3. 性能问题
- 使用子码流以减少带宽占用
- 调整缓冲区大小
- 检查网络带宽

## 下一步

如果您同意使用OpenCV替代方案，我可以：
1. 创建OpenCVCameraManager类
2. 修改MainWindow以支持RTSP地址输入
3. 更新构建脚本
4. 提供完整的测试示例

这个方案的优势是更容易部署和使用，而且支持更多品牌的网络摄像头。
