#include "MainWindow.h"
#include "CameraManager.h"
#include "ConfigManager.h"
#include <QtWidgets/QApplication>
#include <QtCore/QTimer>
#include <QtGui/QCloseEvent>
#include <QtGui/QResizeEvent>

// AddCredentialDialog implementation
AddCredentialDialog::AddCredentialDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("Add Camera Credentials");
    setModal(true);
    resize(300, 150);
    
    auto *layout = new QGridLayout(this);
    
    layout->addWidget(new QLabel("Username:"), 0, 0);
    m_usernameEdit = new QLineEdit(this);
    layout->addWidget(m_usernameEdit, 0, 1);
    
    layout->addWidget(new QLabel("Password:"), 1, 0);
    m_passwordEdit = new QLineEdit(this);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    layout->addWidget(m_passwordEdit, 1, 1);
    
    auto *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, this);
    layout->addWidget(buttonBox, 2, 0, 1, 2);
    
    connect(buttonBox, &QDialogButtonBox::accepted, this, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    
    m_usernameEdit->setFocus();
}

QString AddCredentialDialog::getUsername() const
{
    return m_usernameEdit->text().trimmed();
}

QString AddCredentialDialog::getPassword() const
{
    return m_passwordEdit->text();
}

// MainWindow implementation
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_videoWidget(nullptr)
    , m_videoLabel(nullptr)
    , m_controlPanel(nullptr)
    , m_connectionGroup(nullptr)
    , m_ipAddressEdit(nullptr)
    , m_credentialsCombo(nullptr)
    , m_connectButton(nullptr)
    , m_disconnectButton(nullptr)
    , m_credentialsGroup(nullptr)
    , m_credentialsList(nullptr)
    , m_addCredentialButton(nullptr)
    , m_removeCredentialButton(nullptr)
    , m_statusLabel(nullptr)
    , m_statusTimer(new QTimer(this))
    , m_cameraManager(std::make_unique<CameraManager>(this))
    , m_configManager(std::make_unique<ConfigManager>(this))
    , m_isConnected(false)
{
    setWindowTitle("Hikvision Camera Viewer");
    setMinimumSize(800, 600);
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    loadSettings();
    
    // Initialize camera manager
    if (!m_cameraManager->initialize()) {
        showErrorMessage("Failed to initialize camera SDK. Please ensure Hikvision SDK is properly installed.");
    }
    
    // Set video display widget
    m_cameraManager->setVideoDisplayWidget(m_videoLabel);
    
    // Connect signals
    connect(m_cameraManager.get(), &CameraManager::connectionStatusChanged,
            this, &MainWindow::updateConnectionStatus);
    connect(m_cameraManager.get(), &CameraManager::errorOccurred,
            this, &MainWindow::showErrorMessage);
    connect(m_cameraManager.get(), &CameraManager::frameReceived,
            this, &MainWindow::onVideoFrameReceived);
    
    connect(m_configManager.get(), &ConfigManager::credentialsChanged,
            this, &MainWindow::updateCredentialsList);
    
    // Setup timer for status messages
    m_statusTimer->setSingleShot(true);
    connect(m_statusTimer, &QTimer::timeout, [this]() {
        m_statusLabel->setText("Ready");
    });
    
    updateCredentialsList();
    updateButtonStates();
    
    // Auto-connect if enabled and IP is available
    if (m_configManager->getAutoConnect() && !m_currentIpAddress.isEmpty()) {
        QTimer::singleShot(1000, this, &MainWindow::onConnectClicked);
    }
}

MainWindow::~MainWindow()
{
    if (m_cameraManager) {
        m_cameraManager->disconnectFromCamera();
        m_cameraManager->cleanup();
    }
    saveSettings();
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);

    // Setup video display area
    m_videoWidget = new QWidget(this);
    m_videoWidget->setMinimumSize(640, 480);
    m_videoWidget->setStyleSheet("background-color: black; border: 1px solid gray;");

    auto *videoLayout = new QVBoxLayout(m_videoWidget);
    m_videoLabel = new QLabel(this);
    m_videoLabel->setAlignment(Qt::AlignCenter);
    m_videoLabel->setText("No Video Signal\nClick Connect to start streaming");
    m_videoLabel->setStyleSheet("color: white; font-size: 14px;");
    m_videoLabel->setScaledContents(true);
    videoLayout->addWidget(m_videoLabel);

    // Setup control panel
    m_controlPanel = new QWidget(this);
    m_controlPanel->setFixedWidth(300);
    m_controlPanel->setStyleSheet("QGroupBox { font-weight: bold; }");

    auto *controlLayout = new QVBoxLayout(m_controlPanel);

    // Connection group
    m_connectionGroup = new QGroupBox("Camera Connection", this);
    auto *connectionLayout = new QGridLayout(m_connectionGroup);

    connectionLayout->addWidget(new QLabel("IP Address:"), 0, 0);
    m_ipAddressEdit = new QLineEdit(this);
    m_ipAddressEdit->setPlaceholderText("*************");
    connectionLayout->addWidget(m_ipAddressEdit, 0, 1);

    connectionLayout->addWidget(new QLabel("Credentials:"), 1, 0);
    m_credentialsCombo = new QComboBox(this);
    connectionLayout->addWidget(m_credentialsCombo, 1, 1);

    auto *buttonLayout = new QHBoxLayout();
    m_connectButton = new QPushButton("Connect", this);
    m_disconnectButton = new QPushButton("Disconnect", this);
    buttonLayout->addWidget(m_connectButton);
    buttonLayout->addWidget(m_disconnectButton);
    connectionLayout->addLayout(buttonLayout, 2, 0, 1, 2);

    // Credentials management group
    m_credentialsGroup = new QGroupBox("Manage Credentials", this);
    auto *credentialsLayout = new QVBoxLayout(m_credentialsGroup);

    m_credentialsList = new QListWidget(this);
    m_credentialsList->setMaximumHeight(150);
    credentialsLayout->addWidget(m_credentialsList);

    auto *credButtonLayout = new QHBoxLayout();
    m_addCredentialButton = new QPushButton("Add", this);
    m_removeCredentialButton = new QPushButton("Remove", this);
    credButtonLayout->addWidget(m_addCredentialButton);
    credButtonLayout->addWidget(m_removeCredentialButton);
    credentialsLayout->addLayout(credButtonLayout);

    // Add groups to control panel
    controlLayout->addWidget(m_connectionGroup);
    controlLayout->addWidget(m_credentialsGroup);
    controlLayout->addStretch();

    // Add widgets to splitter
    m_mainSplitter->addWidget(m_videoWidget);
    m_mainSplitter->addWidget(m_controlPanel);
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 0);

    // Set main layout
    auto *mainLayout = new QHBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_mainSplitter);
    mainLayout->setContentsMargins(5, 5, 5, 5);

    // Connect signals
    connect(m_connectButton, &QPushButton::clicked, this, &MainWindow::onConnectClicked);
    connect(m_disconnectButton, &QPushButton::clicked, this, &MainWindow::onDisconnectClicked);
    connect(m_addCredentialButton, &QPushButton::clicked, this, &MainWindow::onAddCredentialClicked);
    connect(m_removeCredentialButton, &QPushButton::clicked, this, &MainWindow::onRemoveCredentialClicked);
    connect(m_credentialsList, &QListWidget::itemSelectionChanged, this, &MainWindow::onCredentialSelectionChanged);
    connect(m_ipAddressEdit, &QLineEdit::textChanged, this, &MainWindow::onIpAddressChanged);
}

void MainWindow::setupMenuBar()
{
    auto *fileMenu = menuBar()->addMenu("&File");

    auto *exitAction = new QAction("E&xit", this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(exitAction);

    auto *viewMenu = menuBar()->addMenu("&View");

    auto *fullscreenAction = new QAction("&Fullscreen", this);
    fullscreenAction->setShortcut(QKeySequence::FullScreen);
    fullscreenAction->setCheckable(true);
    connect(fullscreenAction, &QAction::triggered, [this](bool checked) {
        if (checked) {
            showFullScreen();
        } else {
            showNormal();
        }
    });
    viewMenu->addAction(fullscreenAction);

    auto *helpMenu = menuBar()->addMenu("&Help");

    auto *aboutAction = new QAction("&About", this);
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About Hikvision Camera Viewer",
                          "Hikvision Camera Viewer v1.0.0\n\n"
                          "A simple application to view Hikvision network cameras.\n"
                          "Built with Qt and Hikvision SDK.");
    });
    helpMenu->addAction(aboutAction);
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready", this);
    statusBar()->addWidget(m_statusLabel);

    auto *connectionStatusLabel = new QLabel("Disconnected", this);
    connectionStatusLabel->setStyleSheet("color: red; font-weight: bold;");
    statusBar()->addPermanentWidget(connectionStatusLabel);

    // Update connection status label when connection changes
    connect(m_cameraManager.get(), &CameraManager::connectionStatusChanged,
            [connectionStatusLabel](bool connected) {
                if (connected) {
                    connectionStatusLabel->setText("Connected");
                    connectionStatusLabel->setStyleSheet("color: green; font-weight: bold;");
                } else {
                    connectionStatusLabel->setText("Disconnected");
                    connectionStatusLabel->setStyleSheet("color: red; font-weight: bold;");
                }
            });
}

void MainWindow::loadSettings()
{
    // Restore window geometry and state
    QByteArray geometry = m_configManager->getWindowGeometry();
    if (!geometry.isEmpty()) {
        restoreGeometry(geometry);
    }

    QByteArray state = m_configManager->getWindowState();
    if (!state.isEmpty()) {
        restoreState(state);
    }

    QByteArray splitterState = m_configManager->getSplitterState();
    if (!splitterState.isEmpty()) {
        m_mainSplitter->restoreState(splitterState);
    }

    // Load last IP address
    m_currentIpAddress = m_configManager->getLastIpAddress();
    if (!m_currentIpAddress.isEmpty()) {
        m_ipAddressEdit->setText(m_currentIpAddress);
    }
}

void MainWindow::saveSettings()
{
    // Save window geometry and state
    m_configManager->saveWindowGeometry(saveGeometry());
    m_configManager->saveWindowState(saveState());
    m_configManager->saveSplitterState(m_mainSplitter->saveState());

    // Save current IP address
    if (!m_currentIpAddress.isEmpty()) {
        m_configManager->saveLastIpAddress(m_currentIpAddress);
        m_configManager->addRecentIpAddress(m_currentIpAddress);
    }
}

void MainWindow::onConnectClicked()
{
    QString ipAddress = m_ipAddressEdit->text().trimmed();
    if (ipAddress.isEmpty()) {
        showErrorMessage("Please enter a valid IP address.");
        return;
    }

    int credentialIndex = m_credentialsCombo->currentIndex();
    if (credentialIndex < 0) {
        showErrorMessage("Please select credentials.");
        return;
    }

    auto credentials = m_configManager->getCredentials();
    if (credentialIndex >= credentials.size()) {
        showErrorMessage("Invalid credentials selected.");
        return;
    }

    CameraCredential credential = credentials[credentialIndex];

    showStatusMessage("Connecting to camera...");
    m_connectButton->setEnabled(false);

    // Connect in a separate thread to avoid blocking UI
    QTimer::singleShot(100, [this, ipAddress, credential]() {
        bool success = m_cameraManager->connectToCamera(ipAddress, credential);

        m_connectButton->setEnabled(true);

        if (success) {
            m_currentIpAddress = ipAddress;
            m_isConnected = true;
            showStatusMessage("Connected successfully!");
            m_videoLabel->setText("Loading video stream...");
        } else {
            showErrorMessage(QString("Failed to connect: %1").arg(m_cameraManager->getLastError()));
        }

        updateButtonStates();
    });
}

void MainWindow::onDisconnectClicked()
{
    m_cameraManager->disconnectFromCamera();
    m_isConnected = false;
    m_videoLabel->setText("No Video Signal\nClick Connect to start streaming");
    showStatusMessage("Disconnected from camera.");
    updateButtonStates();
}

void MainWindow::onAddCredentialClicked()
{
    AddCredentialDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        QString username = dialog.getUsername();
        QString password = dialog.getPassword();

        if (username.isEmpty() || password.isEmpty()) {
            showErrorMessage("Username and password cannot be empty.");
            return;
        }

        CameraCredential credential(username, password);
        m_configManager->addCredential(credential);
        updateCredentialsList();
        showStatusMessage("Credential added successfully.");
    }
}

void MainWindow::onRemoveCredentialClicked()
{
    int currentRow = m_credentialsList->currentRow();
    if (currentRow < 0) {
        showErrorMessage("Please select a credential to remove.");
        return;
    }

    auto credentials = m_configManager->getCredentials();
    if (currentRow >= credentials.size()) {
        showErrorMessage("Invalid credential selected.");
        return;
    }

    CameraCredential credential = credentials[currentRow];

    int ret = QMessageBox::question(this, "Remove Credential",
                                   QString("Are you sure you want to remove the credential for user '%1'?")
                                   .arg(credential.username),
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_configManager->removeCredential(credential);
        updateCredentialsList();
        showStatusMessage("Credential removed successfully.");
    }
}

void MainWindow::onCredentialSelectionChanged()
{
    bool hasSelection = m_credentialsList->currentRow() >= 0;
    m_removeCredentialButton->setEnabled(hasSelection);
}

void MainWindow::onIpAddressChanged()
{
    m_currentIpAddress = m_ipAddressEdit->text().trimmed();
}

void MainWindow::updateConnectionStatus()
{
    m_isConnected = m_cameraManager->isConnected();
    updateButtonStates();
}

void MainWindow::onVideoFrameReceived()
{
    // This slot is called when a new video frame is received
    // The actual frame display is handled by the CameraManager
}

void MainWindow::updateCredentialsList()
{
    m_credentialsList->clear();
    m_credentialsCombo->clear();

    auto credentials = m_configManager->getCredentials();
    for (const auto &credential : credentials) {
        QString displayText = QString("%1 (****)").arg(credential.username);
        m_credentialsList->addItem(displayText);
        m_credentialsCombo->addItem(displayText);
    }

    // Select first credential if available
    if (!credentials.isEmpty()) {
        m_credentialsCombo->setCurrentIndex(0);
    }
}

void MainWindow::updateButtonStates()
{
    m_connectButton->setEnabled(!m_isConnected && !m_currentIpAddress.isEmpty());
    m_disconnectButton->setEnabled(m_isConnected);

    bool hasCredentials = m_credentialsCombo->count() > 0;
    m_connectButton->setEnabled(m_connectButton->isEnabled() && hasCredentials);
}

void MainWindow::showErrorMessage(const QString &message)
{
    QMessageBox::warning(this, "Error", message);
    showStatusMessage(QString("Error: %1").arg(message), 5000);
}

void MainWindow::showStatusMessage(const QString &message, int timeout)
{
    m_statusLabel->setText(message);
    if (timeout > 0) {
        m_statusTimer->start(timeout);
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (m_isConnected) {
        m_cameraManager->disconnectFromCamera();
    }
    saveSettings();
    event->accept();
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    // Handle any resize-specific logic here if needed
}

#include "MainWindow.moc"
