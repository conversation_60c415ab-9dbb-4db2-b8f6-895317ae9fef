#include "ConfigManager.h"
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtCore/QCryptographicHash>

// Static member definitions
const QString ConfigManager::KEY_LAST_IP = "connection/lastIpAddress";
const QString ConfigManager::KEY_RECENT_IPS = "connection/recentIpAddresses";
const QString ConfigManager::KEY_CREDENTIALS = "credentials/list";
const QString ConfigManager::KEY_WINDOW_GEOMETRY = "window/geometry";
const QString ConfigManager::KEY_WINDOW_STATE = "window/state";
const QString ConfigManager::KEY_SPLITTER_STATE = "window/splitterState";
const QString ConfigManager::KEY_AUTO_CONNECT = "connection/autoConnect";
const QString ConfigManager::KEY_RECONNECT_ATTEMPTS = "connection/reconnectAttempts";

const QList<CameraCredential> ConfigManager::DEFAULT_CREDENTIALS = {
    CameraCredential("admin", "fssz2016"),
    CameraCredential("admin", "Fssz123456")
};

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
{
    // Create settings with application-specific path
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir configDir(configPath);
    if (!configDir.exists()) {
        configDir.mkpath(".");
    }
    
    QString settingsFile = configDir.absoluteFilePath("config.ini");
    m_settings = new QSettings(settingsFile, QSettings::IniFormat, this);
    
    // Initialize default credentials if none exist
    if (getCredentials().isEmpty()) {
        initializeDefaultCredentials();
    }
}

ConfigManager::~ConfigManager()
{
    if (m_settings) {
        m_settings->sync();
    }
}

void ConfigManager::saveLastIpAddress(const QString &ipAddress)
{
    m_settings->setValue(KEY_LAST_IP, ipAddress);
    m_settings->sync();
}

QString ConfigManager::getLastIpAddress() const
{
    return m_settings->value(KEY_LAST_IP, QString()).toString();
}

void ConfigManager::addRecentIpAddress(const QString &ipAddress)
{
    if (ipAddress.isEmpty()) {
        return;
    }
    
    QStringList recentIps = getRecentIpAddresses();
    
    // Remove if already exists
    recentIps.removeAll(ipAddress);
    
    // Add to front
    recentIps.prepend(ipAddress);
    
    // Limit to MAX_RECENT_IPS
    while (recentIps.size() > MAX_RECENT_IPS) {
        recentIps.removeLast();
    }
    
    m_settings->setValue(KEY_RECENT_IPS, recentIps);
    m_settings->sync();
}

QStringList ConfigManager::getRecentIpAddresses() const
{
    return m_settings->value(KEY_RECENT_IPS, QStringList()).toStringList();
}

void ConfigManager::clearRecentIpAddresses()
{
    m_settings->remove(KEY_RECENT_IPS);
    m_settings->sync();
}

void ConfigManager::addCredential(const CameraCredential &credential)
{
    if (credential.username.isEmpty() || credential.password.isEmpty()) {
        return;
    }
    
    QList<CameraCredential> credentials = getCredentials();
    
    // Check if credential already exists
    for (const auto &existing : credentials) {
        if (existing.username == credential.username && existing.password == credential.password) {
            return; // Already exists
        }
    }
    
    credentials.append(credential);
    
    // Limit to MAX_CREDENTIALS
    while (credentials.size() > MAX_CREDENTIALS) {
        credentials.removeFirst();
    }
    
    // Save credentials
    m_settings->beginWriteArray(KEY_CREDENTIALS);
    for (int i = 0; i < credentials.size(); ++i) {
        m_settings->setArrayIndex(i);
        m_settings->setValue("username", credentials[i].username);
        m_settings->setValue("password", encryptPassword(credentials[i].password));
    }
    m_settings->endArray();
    m_settings->sync();
    
    emit credentialsChanged();
}

void ConfigManager::removeCredential(const CameraCredential &credential)
{
    QList<CameraCredential> credentials = getCredentials();
    
    for (int i = 0; i < credentials.size(); ++i) {
        if (credentials[i].username == credential.username && 
            credentials[i].password == credential.password) {
            credentials.removeAt(i);
            break;
        }
    }
    
    // Save updated credentials
    m_settings->remove(KEY_CREDENTIALS);
    m_settings->beginWriteArray(KEY_CREDENTIALS);
    for (int i = 0; i < credentials.size(); ++i) {
        m_settings->setArrayIndex(i);
        m_settings->setValue("username", credentials[i].username);
        m_settings->setValue("password", encryptPassword(credentials[i].password));
    }
    m_settings->endArray();
    m_settings->sync();
    
    emit credentialsChanged();
}

QList<CameraCredential> ConfigManager::getCredentials() const
{
    QList<CameraCredential> credentials;

    int size = m_settings->beginReadArray(KEY_CREDENTIALS);
    for (int i = 0; i < size; ++i) {
        m_settings->setArrayIndex(i);
        QString username = m_settings->value("username").toString();
        QString encryptedPassword = m_settings->value("password").toString();
        QString password = decryptPassword(encryptedPassword);

        if (!username.isEmpty() && !password.isEmpty()) {
            credentials.append(CameraCredential(username, password));
        }
    }
    m_settings->endArray();

    return credentials;
}

void ConfigManager::clearCredentials()
{
    m_settings->remove(KEY_CREDENTIALS);
    m_settings->sync();
    emit credentialsChanged();
}

void ConfigManager::saveWindowGeometry(const QByteArray &geometry)
{
    m_settings->setValue(KEY_WINDOW_GEOMETRY, geometry);
    m_settings->sync();
}

QByteArray ConfigManager::getWindowGeometry() const
{
    return m_settings->value(KEY_WINDOW_GEOMETRY, QByteArray()).toByteArray();
}

void ConfigManager::saveWindowState(const QByteArray &state)
{
    m_settings->setValue(KEY_WINDOW_STATE, state);
    m_settings->sync();
}

QByteArray ConfigManager::getWindowState() const
{
    return m_settings->value(KEY_WINDOW_STATE, QByteArray()).toByteArray();
}

void ConfigManager::saveSplitterState(const QByteArray &state)
{
    m_settings->setValue(KEY_SPLITTER_STATE, state);
    m_settings->sync();
}

QByteArray ConfigManager::getSplitterState() const
{
    return m_settings->value(KEY_SPLITTER_STATE, QByteArray()).toByteArray();
}

void ConfigManager::saveAutoConnect(bool autoConnect)
{
    m_settings->setValue(KEY_AUTO_CONNECT, autoConnect);
    m_settings->sync();
}

bool ConfigManager::getAutoConnect() const
{
    return m_settings->value(KEY_AUTO_CONNECT, false).toBool();
}

void ConfigManager::saveReconnectAttempts(int attempts)
{
    m_settings->setValue(KEY_RECONNECT_ATTEMPTS, attempts);
    m_settings->sync();
}

int ConfigManager::getReconnectAttempts() const
{
    return m_settings->value(KEY_RECONNECT_ATTEMPTS, 3).toInt();
}

void ConfigManager::initializeDefaultCredentials()
{
    for (const auto &credential : DEFAULT_CREDENTIALS) {
        addCredential(credential);
    }
}

QString ConfigManager::encryptPassword(const QString &password) const
{
    // Simple XOR encryption with a fixed key
    // In a production application, you should use proper encryption
    QByteArray data = password.toUtf8();
    QByteArray key = "HikvisionViewer2024"; // Fixed key

    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ key[i % key.size()];
    }

    return QString::fromLatin1(data.toBase64());
}

QString ConfigManager::decryptPassword(const QString &encryptedPassword) const
{
    // Decrypt using the same XOR method
    QByteArray data = QByteArray::fromBase64(encryptedPassword.toLatin1());
    QByteArray key = "HikvisionViewer2024"; // Same fixed key

    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ key[i % key.size()];
    }

    return QString::fromUtf8(data);
}
