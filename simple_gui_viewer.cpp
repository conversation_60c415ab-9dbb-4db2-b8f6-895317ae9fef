#include <windows.h>
#include <string>
#include <vector>
#include <fstream>

#define ID_IP_EDIT          1001
#define ID_CONNECT_BUTTON   1002
#define ID_CREDENTIAL_COMBO 1003
#define ID_RTSP_EDIT        1004
#define ID_COPY_BUTTON      1005
#define ID_VLC_BUTTON       1006

HWND g_hWnd;
HWND g_hIpEdit;
HWND g_hCredentialCombo;
HWND g_hRtspEdit;

struct Credential {
    std::string username;
    std::string password;
};

std::vector<Credential> g_credentials = {
    {"admin", "fssz2016"},
    {"admin", "Fssz123456"}
};

std::string generateRTSP(const std::string& ip, const Credential& cred) {
    return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
}

void saveLastIP(const std::string& ip) {
    std::ofstream file("camera_config.txt");
    if (file.is_open()) {
        file << "last_ip=" << ip << std::endl;
        file.close();
    }
}

std::string getLastIP() {
    std::ifstream file("camera_config.txt");
    std::string line;
    if (file.is_open()) {
        while (std::getline(file, line)) {
            if (line.find("last_ip=") == 0) {
                return line.substr(8);
            }
        }
        file.close();
    }
    return "";
}

void populateCredentialCombo() {
    SendMessage(g_hCredentialCombo, CB_RESETCONTENT, 0, 0);
    
    for (size_t i = 0; i < g_credentials.size(); ++i) {
        std::string displayText = g_credentials[i].username + " / ****";
        SendMessageA(g_hCredentialCombo, CB_ADDSTRING, 0, (LPARAM)displayText.c_str());
    }
    
    if (!g_credentials.empty()) {
        SendMessage(g_hCredentialCombo, CB_SETCURSEL, 0, 0);
    }
}

void onConnectButton() {
    char ipBuffer[256];
    GetWindowTextA(g_hIpEdit, ipBuffer, sizeof(ipBuffer));
    std::string ip(ipBuffer);
    
    if (ip.empty()) {
        MessageBoxA(g_hWnd, "Please enter a valid IP address", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    int selectedIndex = SendMessage(g_hCredentialCombo, CB_GETCURSEL, 0, 0);
    if (selectedIndex == CB_ERR) {
        MessageBoxA(g_hWnd, "Please select credentials", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    if (selectedIndex >= 0 && selectedIndex < (int)g_credentials.size()) {
        std::string rtspUrl = generateRTSP(ip, g_credentials[selectedIndex]);
        SetWindowTextA(g_hRtspEdit, rtspUrl.c_str());
        saveLastIP(ip);
    }
}

void onCopyButton() {
    char rtspBuffer[512];
    GetWindowTextA(g_hRtspEdit, rtspBuffer, sizeof(rtspBuffer));
    
    if (strlen(rtspBuffer) == 0) {
        MessageBoxA(g_hWnd, "No RTSP URL to copy", "Info", MB_OK | MB_ICONINFORMATION);
        return;
    }
    
    if (OpenClipboard(g_hWnd)) {
        EmptyClipboard();
        
        HGLOBAL hClipboardData = GlobalAlloc(GMEM_DDESHARE, strlen(rtspBuffer) + 1);
        if (hClipboardData) {
            char* pchData = (char*)GlobalLock(hClipboardData);
            strcpy_s(pchData, strlen(rtspBuffer) + 1, rtspBuffer);
            GlobalUnlock(hClipboardData);
            SetClipboardData(CF_TEXT, hClipboardData);
        }
        CloseClipboard();
        MessageBoxA(g_hWnd, "RTSP URL copied to clipboard", "Success", MB_OK | MB_ICONINFORMATION);
    }
}

void onVLCButton() {
    char rtspBuffer[512];
    GetWindowTextA(g_hRtspEdit, rtspBuffer, sizeof(rtspBuffer));
    
    if (strlen(rtspBuffer) == 0) {
        MessageBoxA(g_hWnd, "Please generate RTSP URL first", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    std::string command = "start vlc \"" + std::string(rtspBuffer) + "\"";
    int result = system(command.c_str());
    
    if (result != 0) {
        MessageBoxA(g_hWnd, "Failed to launch VLC. Please ensure VLC is installed.", "Error", MB_OK | MB_ICONWARNING);
    }
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            CreateWindowA("STATIC", "Camera IP Address:", WS_VISIBLE | WS_CHILD,
                         20, 20, 150, 20, hWnd, NULL, NULL, NULL);
            
            g_hIpEdit = CreateWindowA("EDIT", "", WS_VISIBLE | WS_CHILD | WS_BORDER,
                                     20, 45, 200, 25, hWnd, (HMENU)ID_IP_EDIT, NULL, NULL);
            
            CreateWindowA("STATIC", "Credentials:", WS_VISIBLE | WS_CHILD,
                         20, 80, 100, 20, hWnd, NULL, NULL, NULL);
            
            g_hCredentialCombo = CreateWindowA("COMBOBOX", "", 
                                              WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
                                              20, 105, 200, 100, hWnd, (HMENU)ID_CREDENTIAL_COMBO, NULL, NULL);
            
            CreateWindowA("BUTTON", "Generate RTSP", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         240, 45, 100, 30, hWnd, (HMENU)ID_CONNECT_BUTTON, NULL, NULL);
            
            CreateWindowA("STATIC", "Generated RTSP URL:", WS_VISIBLE | WS_CHILD,
                         20, 140, 150, 20, hWnd, NULL, NULL, NULL);
            
            g_hRtspEdit = CreateWindowA("EDIT", "", 
                                       WS_VISIBLE | WS_CHILD | WS_BORDER | ES_READONLY | ES_AUTOHSCROLL,
                                       20, 165, 400, 25, hWnd, (HMENU)ID_RTSP_EDIT, NULL, NULL);
            
            CreateWindowA("BUTTON", "Copy URL", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         430, 165, 80, 25, hWnd, (HMENU)ID_COPY_BUTTON, NULL, NULL);
            
            CreateWindowA("BUTTON", "Open in VLC", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         520, 165, 100, 25, hWnd, (HMENU)ID_VLC_BUTTON, NULL, NULL);
            
            populateCredentialCombo();
            
            std::string lastIP = getLastIP();
            if (!lastIP.empty()) {
                SetWindowTextA(g_hIpEdit, lastIP.c_str());
            }
        }
        break;
        
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CONNECT_BUTTON:
            onConnectButton();
            break;
        case ID_COPY_BUTTON:
            onCopyButton();
            break;
        case ID_VLC_BUTTON:
            onVLCButton();
            break;
        }
        break;
        
    case WM_DESTROY:
        PostQuitMessage(0);
        break;
        
    default:
        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
    return 0;
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    WNDCLASSA wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "HikvisionCameraViewer";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    
    RegisterClassA(&wc);
    
    g_hWnd = CreateWindowExA(
        0,
        "HikvisionCameraViewer",
        "Hikvision Camera Viewer",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 660, 250,
        NULL, NULL, hInstance, NULL
    );
    
    if (g_hWnd == NULL) {
        return 0;
    }
    
    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);
    
    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
