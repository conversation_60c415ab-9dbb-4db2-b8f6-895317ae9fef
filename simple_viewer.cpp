#include <iostream>
#include <string>
#include <vector>
#include <fstream>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#endif

class SimpleConfig {
private:
    std::string configFile = "camera_config.txt";
    
public:
    struct Credential {
        std::string username;
        std::string password;
    };
    
    std::vector<Credential> getDefaultCredentials() {
        return {
            {"admin", "fssz2016"},
            {"admin", "Fssz123456"}
        };
    }
    
    void saveLastIP(const std::string& ip) {
        std::ofstream file(configFile);
        if (file.is_open()) {
            file << "last_ip=" << ip << std::endl;
            file.close();
        }
    }
    
    std::string getLastIP() {
        std::ifstream file(configFile);
        std::string line;
        if (file.is_open()) {
            while (std::getline(file, line)) {
                if (line.find("last_ip=") == 0) {
                    return line.substr(8);
                }
            }
            file.close();
        }
        return "";
    }
};

class SimpleCameraViewer {
private:
    SimpleConfig config;
    std::string currentIP;
    
public:
    void showWelcome() {
        std::cout << "========================================" << std::endl;
        std::cout << "    Hikvision Camera Viewer (Simple)" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
    }
    
    void showCredentials() {
        auto credentials = config.getDefaultCredentials();
        std::cout << "Default Credentials:" << std::endl;
        for (size_t i = 0; i < credentials.size(); ++i) {
            std::cout << "  " << (i + 1) << ". " << credentials[i].username 
                     << " / " << credentials[i].password << std::endl;
        }
        std::cout << std::endl;
    }
    
    std::string generateRTSPUrl(const std::string& ip, const SimpleConfig::Credential& cred) {
        return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
    }
    
    void showRTSPUrls(const std::string& ip) {
        auto credentials = config.getDefaultCredentials();
        std::cout << "生成的RTSP地址:" << std::endl;
        for (const auto& cred : credentials) {
            std::cout << "  " << generateRTSPUrl(ip, cred) << std::endl;
        }
        std::cout << std::endl;
    }
    
    void openWithVLC(const std::string& rtspUrl) {
        std::cout << "尝试使用VLC播放器打开..." << std::endl;
        
#ifdef _WIN32
        std::string command = "start vlc \"" + rtspUrl + "\"";
        int result = system(command.c_str());
        if (result == 0) {
            std::cout << "VLC播放器已启动!" << std::endl;
        } else {
            std::cout << "无法启动VLC播放器。请手动复制RTSP地址到VLC播放器中。" << std::endl;
        }
#else
        std::string command = "vlc \"" + rtspUrl + "\" &";
        int result = system(command.c_str());
        if (result == 0) {
            std::cout << "VLC播放器已启动!" << std::endl;
        } else {
            std::cout << "无法启动VLC播放器。请手动复制RTSP地址到VLC播放器中。" << std::endl;
        }
#endif
    }
    
    void run() {
        showWelcome();
        
        // 显示上次使用的IP
        std::string lastIP = config.getLastIP();
        if (!lastIP.empty()) {
            std::cout << "上次使用的IP地址: " << lastIP << std::endl;
            std::cout << std::endl;
        }
        
        showCredentials();
        
        while (true) {
            std::cout << "请输入摄像头IP地址 (或输入 'quit' 退出): ";
            std::string input;
            std::getline(std::cin, input);
            
            if (input == "quit" || input == "exit") {
                break;
            }
            
            if (input.empty()) {
                if (!lastIP.empty()) {
                    input = lastIP;
                    std::cout << "使用上次的IP地址: " << input << std::endl;
                } else {
                    std::cout << "请输入有效的IP地址。" << std::endl;
                    continue;
                }
            }
            
            currentIP = input;
            config.saveLastIP(currentIP);
            
            std::cout << std::endl;
            showRTSPUrls(currentIP);
            
            std::cout << "选择操作:" << std::endl;
            std::cout << "  1. 使用VLC播放器打开 (凭据1)" << std::endl;
            std::cout << "  2. 使用VLC播放器打开 (凭据2)" << std::endl;
            std::cout << "  3. 显示所有RTSP地址" << std::endl;
            std::cout << "  4. 输入新的IP地址" << std::endl;
            std::cout << "  5. 退出" << std::endl;
            std::cout << "请选择 (1-5): ";
            
            std::string choice;
            std::getline(std::cin, choice);
            
            auto credentials = config.getDefaultCredentials();
            
            if (choice == "1" && credentials.size() > 0) {
                openWithVLC(generateRTSPUrl(currentIP, credentials[0]));
            } else if (choice == "2" && credentials.size() > 1) {
                openWithVLC(generateRTSPUrl(currentIP, credentials[1]));
            } else if (choice == "3") {
                showRTSPUrls(currentIP);
            } else if (choice == "4") {
                continue;
            } else if (choice == "5") {
                break;
            } else {
                std::cout << "无效选择，请重新输入。" << std::endl;
            }
            
            std::cout << std::endl;
        }
        
        std::cout << "感谢使用海康威视摄像头查看器!" << std::endl;
    }
};

int main() {
    // 设置控制台编码为UTF-8 (Windows)
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
#endif
    
    SimpleCameraViewer viewer;
    viewer.run();
    
    return 0;
}
