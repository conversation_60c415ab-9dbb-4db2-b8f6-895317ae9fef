#include "CameraManager.h"
#include <QtCore/QDebug>
#include <QtCore/QThread>
#include <QtGui/QPixmap>
#include <QtWidgets/QApplication>

CameraManager::CameraManager(QObject *parent)
    : QObject(parent)
#ifdef _WIN32
    , m_userID(-1)
    , m_realPlayHandle(-1)
    , m_playPort(-1)
#endif
    , m_isInitialized(false)
    , m_isConnected(false)
    , m_videoWidget(nullptr)
    , m_connectionTimer(new QTimer(this))
{
    m_connectionTimer->setSingleShot(false);
    m_connectionTimer->setInterval(RECONNECT_INTERVAL);
    connect(m_connectionTimer, &QTimer::timeout, this, &CameraManager::checkConnection);
}

CameraManager::~CameraManager()
{
    cleanup();
}

bool CameraManager::initialize()
{
    if (m_isInitialized) {
        return true;
    }

#ifdef _WIN32
#ifdef HIKVISION_SDK_AVAILABLE
    // Initialize Hikvision SDK
    if (!NET_DVR_Init()) {
        m_lastError = "Failed to initialize Hikvision SDK";
        return false;
    }

    // Set connection timeout
    NET_DVR_SetConnectTime(CONNECTION_TIMEOUT, 1);
    NET_DVR_SetReconnect(10000, true);

    // Initialize PlayM4 SDK
    if (!PlayM4_GetPort(&m_playPort)) {
        m_lastError = "Failed to get PlayM4 port";
        NET_DVR_Cleanup();
        return false;
    }
#else
    m_lastError = "Hikvision SDK not available. Please install SDK according to SDK_SETUP.md";
    qDebug() << m_lastError;
    // Continue initialization for UI testing without SDK
#endif
#endif

    m_isInitialized = true;
    return true;
}

void CameraManager::cleanup()
{
    if (!m_isInitialized) {
        return;
    }
    
    disconnectFromCamera();
    
#ifdef _WIN32
    if (m_playPort >= 0) {
        PlayM4_FreePort(m_playPort);
        m_playPort = -1;
    }
    
    NET_DVR_Cleanup();
#endif
    
    m_isInitialized = false;
}

bool CameraManager::connectToCamera(const QString &ipAddress, const CameraCredential &credential)
{
    if (!m_isInitialized) {
        m_lastError = "Camera manager not initialized";
        return false;
    }
    
    if (m_isConnected) {
        disconnectFromCamera();
    }
    
    m_currentIpAddress = ipAddress;
    m_currentCredential = credential;

#ifdef _WIN32
    // Setup login parameters
    NET_DVR_DEVICEINFO_V30 deviceInfo = {0};
    NET_DVR_USER_LOGIN_INFO loginInfo = {0};
    
    // Convert QString to char array
    QByteArray ipBytes = ipAddress.toLocal8Bit();
    QByteArray userBytes = credential.username.toLocal8Bit();
    QByteArray passBytes = credential.password.toLocal8Bit();
    
    strncpy_s(loginInfo.sDeviceAddress, ipBytes.constData(), sizeof(loginInfo.sDeviceAddress) - 1);
    strncpy_s(loginInfo.sUserName, userBytes.constData(), sizeof(loginInfo.sUserName) - 1);
    strncpy_s(loginInfo.sPassword, passBytes.constData(), sizeof(loginInfo.sPassword) - 1);
    loginInfo.wPort = 8000; // Default Hikvision port
    loginInfo.bUseAsynLogin = false;
    
    // Login to device
    m_userID = NET_DVR_Login_V40(&loginInfo, &deviceInfo);
    if (m_userID < 0) {
        DWORD errorCode = NET_DVR_GetLastError();
        m_lastError = QString("Login failed. Error code: %1").arg(errorCode);
        return false;
    }
    
    // Start real-time preview
    NET_DVR_PREVIEWINFO previewInfo = {0};
    previewInfo.hPlayWnd = nullptr; // We'll handle display ourselves
    previewInfo.lChannel = 1; // Main channel
    previewInfo.dwStreamType = 0; // Main stream
    previewInfo.dwLinkMode = 0; // TCP
    previewInfo.bBlocked = 1; // Blocked mode
    
    m_realPlayHandle = NET_DVR_RealPlay_V40(m_userID, &previewInfo, RealDataCallBack, this);
    if (m_realPlayHandle < 0) {
        DWORD errorCode = NET_DVR_GetLastError();
        m_lastError = QString("Failed to start preview. Error code: %1").arg(errorCode);
        NET_DVR_Logout(m_userID);
        m_userID = -1;
        return false;
    }
    
    // Setup playback
    if (!setupPlayback()) {
        NET_DVR_StopRealPlay(m_realPlayHandle);
        NET_DVR_Logout(m_userID);
        m_realPlayHandle = -1;
        m_userID = -1;
        return false;
    }
#else
    // For non-Windows platforms, you would implement alternative camera connection
    // This could be using OpenCV, GStreamer, or other cross-platform solutions
    m_lastError = "Camera connection not implemented for this platform";
    return false;
#endif
    
    m_isConnected = true;
    m_connectionTimer->start();
    
    emit connectionStatusChanged(true);
    return true;
}

void CameraManager::disconnectFromCamera()
{
    if (!m_isConnected) {
        return;
    }

    m_connectionTimer->stop();

#ifdef _WIN32
    cleanupPlayback();

    if (m_realPlayHandle >= 0) {
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
    }

    if (m_userID >= 0) {
        NET_DVR_Logout(m_userID);
        m_userID = -1;
    }
#endif

    m_isConnected = false;
    m_currentIpAddress.clear();

    emit connectionStatusChanged(false);
}

void CameraManager::setVideoDisplayWidget(QLabel *widget)
{
    QMutexLocker locker(&m_frameMutex);
    m_videoWidget = widget;
}

void CameraManager::checkConnection()
{
    if (!m_isConnected) {
        return;
    }

#ifdef _WIN32
    // Check if the connection is still alive
    NET_DVR_DEVICEINFO_V30 deviceInfo = {0};
    if (NET_DVR_GetDVRConfig(m_userID, NET_DVR_GET_DEVICECFG_V30, 0, &deviceInfo, sizeof(deviceInfo), nullptr) < 0) {
        // Connection lost, try to reconnect
        qDebug() << "Connection lost, attempting to reconnect...";

        QString ip = m_currentIpAddress;
        CameraCredential cred = m_currentCredential;

        disconnectFromCamera();

        // Try to reconnect after a short delay
        QTimer::singleShot(1000, [this, ip, cred]() {
            if (connectToCamera(ip, cred)) {
                qDebug() << "Reconnected successfully";
            } else {
                qDebug() << "Reconnection failed:" << m_lastError;
                emit errorOccurred(QString("Connection lost. Reconnection failed: %1").arg(m_lastError));
            }
        });
    }
#endif
}

#ifdef _WIN32
void CALLBACK CameraManager::RealDataCallBack(LONG lRealHandle, DWORD dwDataType,
                                             BYTE *pBuffer, DWORD dwBufSize, void *pUser)
{
    CameraManager *manager = static_cast<CameraManager*>(pUser);
    if (manager && dwDataType == NET_DVR_SYSHEAD) {
        // System header, setup decoder
        if (manager->m_playPort >= 0) {
            if (!PlayM4_SetStreamOpenMode(manager->m_playPort, STREAME_REALTIME)) {
                qDebug() << "PlayM4_SetStreamOpenMode failed";
                return;
            }

            if (!PlayM4_OpenStream(manager->m_playPort, pBuffer, dwBufSize, 1024 * 1024)) {
                qDebug() << "PlayM4_OpenStream failed";
                return;
            }

            if (!PlayM4_SetDecCallBack(manager->m_playPort, DecCallBack, (LONG)manager)) {
                qDebug() << "PlayM4_SetDecCallBack failed";
                return;
            }

            if (!PlayM4_Play(manager->m_playPort, nullptr)) {
                qDebug() << "PlayM4_Play failed";
                return;
            }
        }
    } else if (manager && dwDataType == NET_DVR_STREAMDATA) {
        // Stream data
        if (manager->m_playPort >= 0) {
            if (!PlayM4_InputData(manager->m_playPort, pBuffer, dwBufSize)) {
                // Handle input data error
            }
        }
    }
}

void CALLBACK CameraManager::DecCallBack(LONG nPort, char *pBuf, LONG nSize,
                                        FRAME_INFO *pFrameInfo, LONG nUser, LONG nReserved2)
{
    CameraManager *manager = reinterpret_cast<CameraManager*>(nUser);
    if (manager && pFrameInfo->nType == T_YV12) {
        manager->processVideoFrame(reinterpret_cast<BYTE*>(pBuf), nSize);
    }
}
#endif

void CameraManager::processVideoFrame(BYTE *pBuffer, DWORD dwBufSize)
{
    QMutexLocker locker(&m_frameMutex);

    if (!m_videoWidget) {
        return;
    }

    // This is a simplified implementation
    // In a real application, you would convert the YV12 frame to RGB
    // and create a QPixmap to display in the QLabel

    // For now, just emit a signal to indicate frame received
    emit frameReceived();

    // TODO: Implement proper YV12 to RGB conversion and display
    // This would involve:
    // 1. Converting YV12 format to RGB
    // 2. Creating QImage from RGB data
    // 3. Creating QPixmap from QImage
    // 4. Setting the pixmap to the video widget

    // Example placeholder:
    static int frameCount = 0;
    frameCount++;

    if (frameCount % 30 == 0) { // Update every 30 frames to avoid too frequent updates
        QMetaObject::invokeMethod(m_videoWidget, [this]() {
            if (m_videoWidget) {
                m_videoWidget->setText(QString("Video Stream Active\nFrame: %1").arg(frameCount));
            }
        }, Qt::QueuedConnection);
    }
}

bool CameraManager::setupPlayback()
{
#ifdef _WIN32
    if (m_playPort < 0) {
        m_lastError = "Invalid play port";
        return false;
    }

    // Additional playback setup if needed
    return true;
#else
    return false;
#endif
}

void CameraManager::cleanupPlayback()
{
#ifdef _WIN32
    if (m_playPort >= 0) {
        PlayM4_Stop(m_playPort);
        PlayM4_CloseStream(m_playPort);
    }
#endif
}
