#include <windows.h>
#include <string>
#include <vector>
#include <thread>
#include <fstream>
#include <wininet.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "ws2_32.lib")

#define ID_IP_EDIT          1001
#define ID_CONNECT_BUTTON   1002
#define ID_VIDEO_AREA       1003
#define ID_STATUS_TEXT      1004

HWND g_hWnd;
HWND g_hIpEdit;
HWND g_hVideoArea;
HWND g_hStatusText;
HWND g_hConnectButton;

struct Credential {
    std::string username;
    std::string password;
};

std::vector<Credential> g_credentials = {
    {"admin", "fssz2016"},
    {"admin", "Fssz123456"}
};

bool g_isConnecting = false;
bool g_isConnected = false;
std::thread g_connectionThread;

void updateStatus(const std::string& message) {
    SetWindowTextA(g_hStatusText, message.c_str());
}

void updateVideoDisplay(const std::string& message) {
    // 在视频区域显示文本信息
    HDC hdc = GetDC(g_hVideoArea);
    RECT rect;
    GetClientRect(g_hVideoArea, &rect);
    
    // 清除背景
    FillRect(hdc, &rect, (HBRUSH)GetStockObject(BLACK_BRUSH));
    
    // 设置文本颜色和背景
    SetTextColor(hdc, RGB(255, 255, 255));
    SetBkColor(hdc, RGB(0, 0, 0));
    
    // 居中显示文本
    DrawTextA(hdc, message.c_str(), -1, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
    
    ReleaseDC(g_hVideoArea, hdc);
}

std::string generateRTSP(const std::string& ip, const Credential& cred) {
    return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
}

bool testRTSPConnection(const std::string& rtspUrl) {
    // 简化的连接测试
    Sleep(1000); // 模拟连接延迟

    // 简单的网络连通性测试
    std::string ip;
    size_t atPos = rtspUrl.find("@");
    size_t colonPos = rtspUrl.find(":", atPos);
    if (atPos != std::string::npos && colonPos != std::string::npos) {
        ip = rtspUrl.substr(atPos + 1, colonPos - atPos - 1);
    }

    if (ip.empty()) return false;

    // 使用ping测试网络连通性
    std::string pingCmd = "ping -n 1 -w 1000 " + ip + " >nul 2>&1";
    int result = system(pingCmd.c_str());

    return (result == 0);
}

void launchVLCWithRTSP(const std::string& rtspUrl) {
    // 启动VLC并全屏播放
    std::string command = "start vlc --intf dummy --fullscreen \"" + rtspUrl + "\"";
    system(command.c_str());
}

void connectToCamera(const std::string& ip) {
    g_isConnecting = true;
    updateStatus("Connecting to camera...");
    updateVideoDisplay("Connecting to camera...\nPlease wait...");
    
    bool connected = false;
    std::string workingRTSP;
    
    for (const auto& cred : g_credentials) {
        if (!g_isConnecting) break; // 用户取消了连接
        
        std::string rtspUrl = generateRTSP(ip, cred);
        std::string statusMsg = "Trying: " + cred.username + " / " + cred.password;
        updateStatus(statusMsg);
        updateVideoDisplay("Trying credentials:\n" + cred.username + " / " + cred.password);
        
        if (testRTSPConnection(rtspUrl)) {
            connected = true;
            workingRTSP = rtspUrl;
            break;
        }
    }
    
    if (connected && g_isConnecting) {
        g_isConnected = true;
        updateStatus("Connected! Starting video stream...");
        updateVideoDisplay("Connected!\nStarting VLC player...");
        
        // 保存成功的IP
        std::ofstream file("camera_config.txt");
        if (file.is_open()) {
            file << "last_ip=" << ip << std::endl;
            file.close();
        }
        
        // 启动VLC播放器
        Sleep(1000);
        launchVLCWithRTSP(workingRTSP);
        
        updateVideoDisplay("Video stream opened in VLC player\n\nRTSP URL:\n" + workingRTSP);
        updateStatus("Video stream active in VLC");
        
    } else if (g_isConnecting) {
        updateStatus("Connection failed - Check IP and network");
        updateVideoDisplay("Connection Failed\n\nPlease check:\n- Camera IP address\n- Network connection\n- Camera is online");
    }
    
    g_isConnecting = false;
    EnableWindow(g_hConnectButton, TRUE);
    SetWindowTextA(g_hConnectButton, g_isConnected ? "Reconnect" : "Connect");
}

void onConnectButton() {
    if (g_isConnecting) {
        // 取消连接
        g_isConnecting = false;
        updateStatus("Connection cancelled");
        return;
    }
    
    char ipBuffer[256];
    GetWindowTextA(g_hIpEdit, ipBuffer, sizeof(ipBuffer));
    std::string ip(ipBuffer);
    
    if (ip.empty()) {
        MessageBoxA(g_hWnd, "Please enter camera IP address", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    // 验证IP格式（简单验证）
    bool validIP = true;
    int dotCount = 0;
    for (char c : ip) {
        if (c == '.') dotCount++;
        else if (!isdigit(c)) {
            validIP = false;
            break;
        }
    }
    if (dotCount != 3) validIP = false;
    
    if (!validIP) {
        MessageBoxA(g_hWnd, "Please enter a valid IP address (e.g., *************)", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    g_isConnected = false;
    EnableWindow(g_hConnectButton, TRUE);
    SetWindowTextA(g_hConnectButton, "Cancel");
    
    // 在新线程中连接摄像头
    if (g_connectionThread.joinable()) {
        g_connectionThread.join();
    }
    g_connectionThread = std::thread(connectToCamera, ip);
}

std::string getLastIP() {
    std::ifstream file("camera_config.txt");
    std::string line;
    if (file.is_open()) {
        while (std::getline(file, line)) {
            if (line.find("last_ip=") == 0) {
                return line.substr(8);
            }
        }
        file.close();
    }
    return "";
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            // 创建IP输入区域
            CreateWindowA("STATIC", "Camera IP Address:", WS_VISIBLE | WS_CHILD,
                         20, 20, 150, 20, hWnd, NULL, NULL, NULL);
            
            g_hIpEdit = CreateWindowA("EDIT", "", WS_VISIBLE | WS_CHILD | WS_BORDER,
                                     20, 45, 200, 25, hWnd, (HMENU)ID_IP_EDIT, NULL, NULL);
            
            g_hConnectButton = CreateWindowA("BUTTON", "Connect", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                            240, 45, 80, 25, hWnd, (HMENU)ID_CONNECT_BUTTON, NULL, NULL);
            
            // 创建视频显示区域
            g_hVideoArea = CreateWindowA("STATIC", "", 
                                        WS_VISIBLE | WS_CHILD | WS_BORDER | SS_CENTER,
                                        20, 85, 560, 320, hWnd, (HMENU)ID_VIDEO_AREA, NULL, NULL);
            
            // 状态栏
            g_hStatusText = CreateWindowA("STATIC", "Ready - Enter camera IP and click Connect", 
                                         WS_VISIBLE | WS_CHILD,
                                         20, 420, 500, 20, hWnd, (HMENU)ID_STATUS_TEXT, NULL, NULL);
            
            // 初始化视频区域
            updateVideoDisplay("Camera Monitor\n\nEnter IP address and click Connect\nto start monitoring");
            
            // 加载上次的IP
            std::string lastIP = getLastIP();
            if (!lastIP.empty()) {
                SetWindowTextA(g_hIpEdit, lastIP.c_str());
            }
        }
        break;
        
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CONNECT_BUTTON:
            onConnectButton();
            break;
        }
        break;
        
    case WM_DESTROY:
        g_isConnecting = false;
        if (g_connectionThread.joinable()) {
            g_connectionThread.join();
        }
        PostQuitMessage(0);
        break;
        
    default:
        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
    return 0;
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    WNDCLASSA wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "DirectCameraViewer";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    
    RegisterClassA(&wc);
    
    g_hWnd = CreateWindowExA(
        0,
        "DirectCameraViewer",
        "Hikvision Camera Monitor",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 620, 480,
        NULL, NULL, hInstance, NULL
    );
    
    if (g_hWnd == NULL) {
        return 0;
    }
    
    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);
    
    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
