#include "OpenCVCameraManager.h"
#include <QtCore/QDebug>
#include <QtCore/QThread>
#include <QtGui/QPixmap>
#include <QtGui/QImage>
#include <QtWidgets/QApplication>

OpenCVCameraManager::OpenCVCameraManager(QObject *parent)
    : QObject(parent)
#ifdef OPENCV_AVAILABLE
    , m_capture(nullptr)
#endif
    , m_isInitialized(false)
    , m_isConnected(false)
    , m_videoWidget(nullptr)
    , m_captureTimer(new QTimer(this))
    , m_connectionTimer(new QTimer(this))
    , m_frameRate(DEFAULT_FRAME_RATE)
    , m_reconnectInterval(DEFAULT_RECONNECT_INTERVAL)
    , m_connectionTimeout(DEFAULT_CONNECTION_TIMEOUT)
{
    // Setup capture timer
    m_captureTimer->setSingleShot(false);
    connect(m_captureTimer, &QTimer::timeout, this, &OpenCVCameraManager::captureFrame);
    
    // Setup connection monitoring timer
    m_connectionTimer->setSingleShot(false);
    m_connectionTimer->setInterval(m_reconnectInterval);
    connect(m_connectionTimer, &QTimer::timeout, this, &OpenCVCameraManager::checkConnection);
}

OpenCVCameraManager::~OpenCVCameraManager()
{
    cleanup();
}

bool OpenCVCameraManager::initialize()
{
    if (m_isInitialized) {
        return true;
    }

#ifdef OPENCV_AVAILABLE
    qDebug() << "OpenCV Camera Manager initialized successfully";
    m_isInitialized = true;
    return true;
#else
    m_lastError = "OpenCV not available. Please install OpenCV according to ALTERNATIVE_SOLUTION.md";
    qDebug() << m_lastError;
    return false;
#endif
}

void OpenCVCameraManager::cleanup()
{
    if (!m_isInitialized) {
        return;
    }
    
    disconnectFromCamera();
    m_isInitialized = false;
}

bool OpenCVCameraManager::connectToCamera(const QString &rtspUrl)
{
    if (!m_isInitialized) {
        m_lastError = "Camera manager not initialized";
        return false;
    }
    
    if (m_isConnected) {
        disconnectFromCamera();
    }
    
    m_currentRtspUrl = rtspUrl;

#ifdef OPENCV_AVAILABLE
    try {
        m_capture = std::make_unique<cv::VideoCapture>();
        
        // Set buffer size to reduce latency
        m_capture->set(cv::CAP_PROP_BUFFERSIZE, 1);
        
        // Set timeout for connection
        m_capture->set(cv::CAP_PROP_OPEN_TIMEOUT_MSEC, m_connectionTimeout);
        
        // Open the RTSP stream
        if (!m_capture->open(rtspUrl.toStdString())) {
            m_lastError = QString("Failed to open RTSP stream: %1").arg(rtspUrl);
            m_capture.reset();
            return false;
        }
        
        // Check if the stream is opened successfully
        if (!m_capture->isOpened()) {
            m_lastError = "RTSP stream opened but not ready";
            m_capture.reset();
            return false;
        }
        
        // Try to read the first frame to verify connection
        cv::Mat testFrame;
        if (!m_capture->read(testFrame) || testFrame.empty()) {
            m_lastError = "Failed to read from RTSP stream";
            m_capture.reset();
            return false;
        }
        
        m_isConnected = true;
        startCapture();
        m_connectionTimer->start();
        
        emit connectionStatusChanged(true);
        return true;
        
    } catch (const cv::Exception &e) {
        m_lastError = QString("OpenCV exception: %1").arg(e.what());
        m_capture.reset();
        return false;
    } catch (const std::exception &e) {
        m_lastError = QString("Standard exception: %1").arg(e.what());
        m_capture.reset();
        return false;
    }
#else
    m_lastError = "OpenCV not available";
    return false;
#endif
}

bool OpenCVCameraManager::connectToCamera(const QString &ipAddress, const CameraCredential &credential)
{
    QString rtspUrl = generateRtspUrl(ipAddress, credential);
    return connectToCamera(rtspUrl);
}

void OpenCVCameraManager::disconnectFromCamera()
{
    if (!m_isConnected) {
        return;
    }
    
    stopCapture();
    m_connectionTimer->stop();
    
#ifdef OPENCV_AVAILABLE
    if (m_capture) {
        m_capture->release();
        m_capture.reset();
    }
#endif
    
    m_isConnected = false;
    m_currentRtspUrl.clear();
    
    emit connectionStatusChanged(false);
}

void OpenCVCameraManager::setVideoDisplayWidget(QLabel *widget)
{
    QMutexLocker locker(&m_frameMutex);
    m_videoWidget = widget;
}

QString OpenCVCameraManager::generateRtspUrl(const QString &ipAddress, const CameraCredential &credential, 
                                            int channel, int port)
{
    // Generate RTSP URL for Hikvision cameras
    // Format: rtsp://username:password@ip:port/Streaming/Channels/channel
    return QString("rtsp://%1:%2@%3:%4/Streaming/Channels/%5")
           .arg(credential.username)
           .arg(credential.password)
           .arg(ipAddress)
           .arg(port)
           .arg(channel);
}

void OpenCVCameraManager::startCapture()
{
    int interval = 1000 / m_frameRate; // Convert FPS to milliseconds
    m_captureTimer->start(interval);
}

void OpenCVCameraManager::stopCapture()
{
    m_captureTimer->stop();
}

void OpenCVCameraManager::captureFrame()
{
    if (!m_isConnected) {
        return;
    }

#ifdef OPENCV_AVAILABLE
    if (!m_capture || !m_capture->isOpened()) {
        return;
    }

    try {
        cv::Mat frame;
        if (m_capture->read(frame) && !frame.empty()) {
            QMutexLocker locker(&m_frameMutex);
            m_currentFrame = frame.clone();

            if (m_videoWidget) {
                // Convert OpenCV Mat to QPixmap and display
                QPixmap pixmap = matToQPixmap(frame);

                // Scale pixmap to fit the widget while maintaining aspect ratio
                QSize widgetSize = m_videoWidget->size();
                QPixmap scaledPixmap = pixmap.scaled(widgetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);

                // Update the widget in the main thread
                QMetaObject::invokeMethod(m_videoWidget, [this, scaledPixmap]() {
                    if (m_videoWidget) {
                        m_videoWidget->setPixmap(scaledPixmap);
                    }
                }, Qt::QueuedConnection);
            }

            emit frameReceived();
        } else {
            // Failed to read frame, might be connection issue
            qDebug() << "Failed to read frame from RTSP stream";
        }
    } catch (const cv::Exception &e) {
        qDebug() << "OpenCV exception in captureFrame:" << e.what();
        emit errorOccurred(QString("Frame capture error: %1").arg(e.what()));
    }
#endif
}

void OpenCVCameraManager::checkConnection()
{
    if (!m_isConnected) {
        return;
    }

#ifdef OPENCV_AVAILABLE
    if (!m_capture || !m_capture->isOpened()) {
        qDebug() << "Connection lost, attempting to reconnect...";

        QString rtspUrl = m_currentRtspUrl;
        disconnectFromCamera();

        // Try to reconnect after a short delay
        QTimer::singleShot(1000, [this, rtspUrl]() {
            if (connectToCamera(rtspUrl)) {
                qDebug() << "Reconnected successfully";
            } else {
                qDebug() << "Reconnection failed:" << m_lastError;
                emit errorOccurred(QString("Connection lost. Reconnection failed: %1").arg(m_lastError));
            }
        });
    }
#endif
}

QPixmap OpenCVCameraManager::matToQPixmap(const cv::Mat &mat)
{
#ifdef OPENCV_AVAILABLE
    try {
        cv::Mat rgbMat;

        // Convert color space based on the number of channels
        if (mat.channels() == 3) {
            cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB);
        } else if (mat.channels() == 4) {
            cv::cvtColor(mat, rgbMat, cv::COLOR_BGRA2RGB);
        } else if (mat.channels() == 1) {
            cv::cvtColor(mat, rgbMat, cv::COLOR_GRAY2RGB);
        } else {
            rgbMat = mat;
        }

        // Create QImage from OpenCV Mat
        QImage qimg(rgbMat.data, rgbMat.cols, rgbMat.rows, rgbMat.step, QImage::Format_RGB888);

        // Convert QImage to QPixmap
        return QPixmap::fromImage(qimg);

    } catch (const cv::Exception &e) {
        qDebug() << "Error converting Mat to QPixmap:" << e.what();
        return QPixmap();
    }
#else
    Q_UNUSED(mat)
    return QPixmap();
#endif
}
