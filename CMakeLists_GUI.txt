cmake_minimum_required(VERSION 3.16)
project(HikvisionCameraViewerGUI)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 创建GUI版摄像头查看器
add_executable(gui_camera_viewer WIN32 gui_camera_viewer.cpp)

# Windows特定设置
if(WIN32)
    # 链接Windows API库
    target_link_libraries(gui_camera_viewer 
        user32 
        gdi32 
        comctl32
    )
    
    set_target_properties(gui_camera_viewer PROPERTIES
        WIN32_EXECUTABLE TRUE  # Windows GUI程序
    )
endif()

message(STATUS "GUI Camera Viewer configured successfully!")
message(STATUS "This version provides a Windows GUI interface")
