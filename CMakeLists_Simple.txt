cmake_minimum_required(VERSION 3.16)
project(HikvisionViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 简化版本 - 不依赖Qt，仅测试编译
message(STATUS "Testing basic compilation...")

# 检查编译器
if(NOT CMAKE_CXX_COMPILER)
    message(FATAL_ERROR "No C++ compiler found!")
endif()

# 创建一个简单的测试程序
set(TEST_SOURCES
    test_main.cpp
)

# 创建测试可执行文件
add_executable(test_app ${TEST_SOURCES})

message(STATUS "Configuration completed successfully!")
message(STATUS "C++ Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
