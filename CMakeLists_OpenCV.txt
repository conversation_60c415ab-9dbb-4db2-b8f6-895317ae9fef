cmake_minimum_required(VERSION 3.16)
project(HikvisionViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)

# Try to find OpenCV
find_package(OpenCV QUIET)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files - choose based on available libraries
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/ConfigManager.cpp
)

set(HEADERS
    include/MainWindow.h
    include/ConfigManager.h
)

# Add camera manager based on available libraries
if(OpenCV_FOUND)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    list(APPEND SOURCES src/OpenCVCameraManager.cpp)
    list(APPEND HEADERS include/OpenCVCameraManager.h)
    set(CAMERA_MANAGER_TYPE "OpenCV")
else()
    message(STATUS "OpenCV not found, using Hikvision SDK")
    list(APPEND SOURCES src/CameraManager.cpp)
    list(APPEND HEADERS include/CameraManager.h)
    set(CAMERA_MANAGER_TYPE "Hikvision")
endif()

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} 
    Qt6::Core 
    Qt6::Widgets 
    Qt6::Network
)

# Platform and library specific settings
if(WIN32)
    # Windows specific settings
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    if(OpenCV_FOUND)
        # OpenCV configuration
        message(STATUS "Configuring with OpenCV support")
        target_compile_definitions(${PROJECT_NAME} PRIVATE OPENCV_AVAILABLE)
        target_include_directories(${PROJECT_NAME} PRIVATE ${OpenCV_INCLUDE_DIRS})
        target_link_libraries(${PROJECT_NAME} ${OpenCV_LIBS})
        
    else()
        # Hikvision SDK configuration
        message(STATUS "Configuring with Hikvision SDK support")
        
        set(HIKVISION_LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib)
        set(HIKVISION_DLL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib/dll)
        
        if(EXISTS ${HIKVISION_LIB_DIR}/HCNetSDK.lib AND EXISTS ${HIKVISION_LIB_DIR}/PlayCtrl.lib)
            message(STATUS "Hikvision SDK libraries found")
            target_compile_definitions(${PROJECT_NAME} PRIVATE HIKVISION_SDK_AVAILABLE)
            
            # Add Hikvision SDK libraries
            target_link_libraries(${PROJECT_NAME}
                ${HIKVISION_LIB_DIR}/HCNetSDK.lib
                ${HIKVISION_LIB_DIR}/PlayCtrl.lib
            )
            
            # Copy DLLs to output directory if they exist
            if(EXISTS ${HIKVISION_DLL_DIR})
                add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory
                    ${HIKVISION_DLL_DIR}
                    $<TARGET_FILE_DIR:${PROJECT_NAME}>
                    COMMENT "Copying Hikvision SDK DLLs"
                )
            endif()
        else()
            message(WARNING "Hikvision SDK libraries not found in ${HIKVISION_LIB_DIR}")
            message(WARNING "Please download and install Hikvision SDK according to SDK_SETUP.md")
            message(WARNING "Or install OpenCV for alternative camera support")
        endif()
    endif()
endif()

# Create directories for build
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include)

# Print configuration summary
message(STATUS "")
message(STATUS "=== Configuration Summary ===")
message(STATUS "Camera Manager: ${CAMERA_MANAGER_TYPE}")
if(OpenCV_FOUND)
    message(STATUS "OpenCV Version: ${OpenCV_VERSION}")
    message(STATUS "OpenCV Libraries: ${OpenCV_LIBS}")
endif()
message(STATUS "Qt Version: ${Qt6_VERSION}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "=============================")
message(STATUS "")
