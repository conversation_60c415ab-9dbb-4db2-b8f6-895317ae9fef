#include <iostream>
#include <string>
#include <vector>
#include <fstream>

class CameraViewer {
private:
    std::string configFile = "camera_config.txt";
    
    struct Credential {
        std::string username;
        std::string password;
    };
    
    std::vector<Credential> getCredentials() {
        return {
            {"admin", "fssz2016"},
            {"admin", "Fssz123456"}
        };
    }
    
    void saveLastIP(const std::string& ip) {
        std::ofstream file(configFile);
        if (file.is_open()) {
            file << "last_ip=" << ip << std::endl;
            file.close();
        }
    }
    
    std::string getLastIP() {
        std::ifstream file(configFile);
        std::string line;
        if (file.is_open()) {
            while (std::getline(file, line)) {
                if (line.find("last_ip=") == 0) {
                    return line.substr(8);
                }
            }
            file.close();
        }
        return "";
    }
    
    std::string generateRTSP(const std::string& ip, const Credential& cred) {
        return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
    }
    
public:
    void run() {
        std::cout << "========================================" << std::endl;
        std::cout << "    Hikvision Camera Viewer" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        
        auto credentials = getCredentials();
        std::cout << "Default Credentials:" << std::endl;
        for (size_t i = 0; i < credentials.size(); ++i) {
            std::cout << "  " << (i + 1) << ". " << credentials[i].username 
                     << " / " << credentials[i].password << std::endl;
        }
        std::cout << std::endl;
        
        std::string lastIP = getLastIP();
        if (!lastIP.empty()) {
            std::cout << "Last used IP: " << lastIP << std::endl;
            std::cout << std::endl;
        }
        
        while (true) {
            std::cout << "Enter camera IP address (or 'quit' to exit): ";
            std::string input;
            std::getline(std::cin, input);
            
            if (input == "quit" || input == "exit") {
                break;
            }
            
            if (input.empty() && !lastIP.empty()) {
                input = lastIP;
                std::cout << "Using last IP: " << input << std::endl;
            }
            
            if (input.empty()) {
                std::cout << "Please enter a valid IP address." << std::endl;
                continue;
            }
            
            saveLastIP(input);
            
            std::cout << std::endl;
            std::cout << "Generated RTSP URLs:" << std::endl;
            for (size_t i = 0; i < credentials.size(); ++i) {
                std::cout << "  " << (i + 1) << ". " << generateRTSP(input, credentials[i]) << std::endl;
            }
            std::cout << std::endl;
            
            std::cout << "Instructions:" << std::endl;
            std::cout << "1. Copy any RTSP URL above" << std::endl;
            std::cout << "2. Open VLC Media Player" << std::endl;
            std::cout << "3. Go to Media -> Open Network Stream" << std::endl;
            std::cout << "4. Paste the RTSP URL and click Play" << std::endl;
            std::cout << std::endl;
            
            std::cout << "Press Enter to continue...";
            std::cin.get();
        }
        
        std::cout << "Thank you for using Hikvision Camera Viewer!" << std::endl;
    }
};

int main() {
    CameraViewer viewer;
    viewer.run();
    return 0;
}
