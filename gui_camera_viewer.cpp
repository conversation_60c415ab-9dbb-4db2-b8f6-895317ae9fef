#include <windows.h>
#include <string>
#include <vector>
#include <fstream>
#include <sstream>

// 控件ID定义
#define ID_IP_EDIT          1001
#define ID_CONNECT_BUTTON   1002
#define ID_CREDENTIAL_COMBO 1003
#define ID_RTSP_EDIT        1004
#define ID_COPY_BUTTON      1005
#define ID_VLC_BUTTON       1006
#define ID_ADD_CRED_BUTTON  1007
#define ID_STATUS_TEXT      1008

// 全局变量
HWND g_hWnd;
HWND g_hIpEdit;
HWND g_hCredentialCombo;
HWND g_hRtspEdit;
HWND g_hStatusText;

struct Credential {
    std::string username;
    std::string password;
    
    std::string toString() const {
        return username + " / ****";
    }
};

class CameraConfig {
private:
    std::string configFile = "camera_config.txt";
    std::vector<Credential> credentials;
    
public:
    CameraConfig() {
        // 默认凭据
        credentials.push_back({"admin", "fssz2016"});
        credentials.push_back({"admin", "Fssz123456"});
        loadConfig();
    }
    
    const std::vector<Credential>& getCredentials() const {
        return credentials;
    }
    
    void addCredential(const Credential& cred) {
        credentials.push_back(cred);
        saveConfig();
    }
    
    void saveLastIP(const std::string& ip) {
        std::ofstream file(configFile);
        if (file.is_open()) {
            file << "last_ip=" << ip << std::endl;
            for (const auto& cred : credentials) {
                file << "credential=" << cred.username << ":" << cred.password << std::endl;
            }
            file.close();
        }
    }
    
    std::string getLastIP() {
        std::ifstream file(configFile);
        std::string line;
        if (file.is_open()) {
            while (std::getline(file, line)) {
                if (line.find("last_ip=") == 0) {
                    return line.substr(8);
                }
            }
            file.close();
        }
        return "";
    }
    
private:
    void loadConfig() {
        std::ifstream file(configFile);
        std::string line;
        if (file.is_open()) {
            while (std::getline(file, line)) {
                if (line.find("credential=") == 0) {
                    std::string credStr = line.substr(11);
                    size_t colonPos = credStr.find(':');
                    if (colonPos != std::string::npos) {
                        std::string username = credStr.substr(0, colonPos);
                        std::string password = credStr.substr(colonPos + 1);
                        
                        // 检查是否已存在
                        bool exists = false;
                        for (const auto& cred : credentials) {
                            if (cred.username == username && cred.password == password) {
                                exists = true;
                                break;
                            }
                        }
                        if (!exists) {
                            credentials.push_back({username, password});
                        }
                    }
                }
            }
            file.close();
        }
    }
    
    void saveConfig() {
        // saveLastIP 中已包含保存逻辑
    }
};

CameraConfig g_config;

std::string generateRTSP(const std::string& ip, const Credential& cred) {
    return "rtsp://" + cred.username + ":" + cred.password + "@" + ip + ":554/Streaming/Channels/101";
}

void updateStatus(const std::string& message) {
    SetWindowTextA(g_hStatusText, message.c_str());
}

void populateCredentialCombo() {
    SendMessage(g_hCredentialCombo, CB_RESETCONTENT, 0, 0);
    
    const auto& credentials = g_config.getCredentials();
    for (const auto& cred : credentials) {
        std::string displayText = cred.toString();
        SendMessageA(g_hCredentialCombo, CB_ADDSTRING, 0, (LPARAM)displayText.c_str());
    }
    
    if (!credentials.empty()) {
        SendMessage(g_hCredentialCombo, CB_SETCURSEL, 0, 0);
    }
}

void onConnectButton() {
    char ipBuffer[256];
    GetWindowTextA(g_hIpEdit, ipBuffer, sizeof(ipBuffer));
    std::string ip(ipBuffer);
    
    if (ip.empty()) {
        updateStatus("Please enter IP address");
        MessageBoxA(g_hWnd, "Please enter a valid IP address", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    int selectedIndex = SendMessage(g_hCredentialCombo, CB_GETCURSEL, 0, 0);
    if (selectedIndex == CB_ERR) {
        updateStatus("Please select credentials");
        MessageBoxA(g_hWnd, "Please select credentials", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    const auto& credentials = g_config.getCredentials();
    if (selectedIndex >= 0 && selectedIndex < (int)credentials.size()) {
        std::string rtspUrl = generateRTSP(ip, credentials[selectedIndex]);
        SetWindowTextA(g_hRtspEdit, rtspUrl.c_str());
        
        g_config.saveLastIP(ip);
        updateStatus("RTSP URL generated successfully");
    }
}

void onCopyButton() {
    char rtspBuffer[512];
    GetWindowTextA(g_hRtspEdit, rtspBuffer, sizeof(rtspBuffer));
    
    if (strlen(rtspBuffer) == 0) {
        updateStatus("No RTSP URL to copy");
        return;
    }
    
    if (OpenClipboard(g_hWnd)) {
        EmptyClipboard();
        
        HGLOBAL hClipboardData = GlobalAlloc(GMEM_DDESHARE, strlen(rtspBuffer) + 1);
        if (hClipboardData) {
            char* pchData = (char*)GlobalLock(hClipboardData);
            strcpy_s(pchData, strlen(rtspBuffer) + 1, rtspBuffer);
            GlobalUnlock(hClipboardData);
            SetClipboardData(CF_TEXT, hClipboardData);
        }
        CloseClipboard();
        updateStatus("RTSP URL copied to clipboard");
    }
}

void onVLCButton() {
    char rtspBuffer[512];
    GetWindowTextA(g_hRtspEdit, rtspBuffer, sizeof(rtspBuffer));
    
    if (strlen(rtspBuffer) == 0) {
        updateStatus("Generate RTSP URL first");
        MessageBoxA(g_hWnd, "Please generate RTSP URL first", "Error", MB_OK | MB_ICONWARNING);
        return;
    }
    
    std::string command = "start vlc \"" + std::string(rtspBuffer) + "\"";
    int result = system(command.c_str());
    
    if (result == 0) {
        updateStatus("VLC launched successfully");
    } else {
        updateStatus("Failed to launch VLC");
        MessageBoxA(g_hWnd, "Failed to launch VLC. Please ensure VLC is installed.", "Error", MB_OK | MB_ICONWARNING);
    }
}

void onAddCredentialButton() {
    MessageBoxA(g_hWnd,
        "To add new credentials:\n"
        "1. Edit camera_config.txt file\n"
        "2. Add line: credential=username:password\n"
        "3. Restart the application\n\n"
        "Example: credential=admin:newpassword",
        "Add Credentials",
        MB_OK | MB_ICONINFORMATION);
}



// 窗口过程函数
LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        {
            // 创建控件
            CreateWindowA("STATIC", "Camera IP Address:", WS_VISIBLE | WS_CHILD,
                         20, 20, 150, 20, hWnd, NULL, NULL, NULL);

            g_hIpEdit = CreateWindowA("EDIT", "", WS_VISIBLE | WS_CHILD | WS_BORDER,
                                     20, 45, 200, 25, hWnd, (HMENU)ID_IP_EDIT, NULL, NULL);

            CreateWindowA("STATIC", "Credentials:", WS_VISIBLE | WS_CHILD,
                         20, 80, 100, 20, hWnd, NULL, NULL, NULL);

            g_hCredentialCombo = CreateWindowA("COMBOBOX", "",
                                              WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
                                              20, 105, 200, 100, hWnd, (HMENU)ID_CREDENTIAL_COMBO, NULL, NULL);

            CreateWindowA("BUTTON", "Generate RTSP", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         240, 45, 100, 30, hWnd, (HMENU)ID_CONNECT_BUTTON, NULL, NULL);

            CreateWindowA("BUTTON", "Add Credential", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         240, 105, 100, 25, hWnd, (HMENU)ID_ADD_CRED_BUTTON, NULL, NULL);

            CreateWindowA("STATIC", "Generated RTSP URL:", WS_VISIBLE | WS_CHILD,
                         20, 140, 150, 20, hWnd, NULL, NULL, NULL);

            g_hRtspEdit = CreateWindowA("EDIT", "",
                                       WS_VISIBLE | WS_CHILD | WS_BORDER | ES_READONLY | ES_AUTOHSCROLL,
                                       20, 165, 400, 25, hWnd, (HMENU)ID_RTSP_EDIT, NULL, NULL);

            CreateWindowA("BUTTON", "Copy URL", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         430, 165, 80, 25, hWnd, (HMENU)ID_COPY_BUTTON, NULL, NULL);

            CreateWindowA("BUTTON", "Open in VLC", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                         520, 165, 100, 25, hWnd, (HMENU)ID_VLC_BUTTON, NULL, NULL);

            g_hStatusText = CreateWindowA("STATIC", "Ready", WS_VISIBLE | WS_CHILD,
                                         20, 210, 400, 20, hWnd, (HMENU)ID_STATUS_TEXT, NULL, NULL);

            // 初始化
            populateCredentialCombo();

            // 加载上次的IP
            std::string lastIP = g_config.getLastIP();
            if (!lastIP.empty()) {
                SetWindowTextA(g_hIpEdit, lastIP.c_str());
            }
        }
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_CONNECT_BUTTON:
            onConnectButton();
            break;
        case ID_COPY_BUTTON:
            onCopyButton();
            break;
        case ID_VLC_BUTTON:
            onVLCButton();
            break;
        case ID_ADD_CRED_BUTTON:
            onAddCredentialButton();
            break;
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
    return 0;
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Register window class
    WNDCLASSA wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "HikvisionCameraViewer";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);

    RegisterClassA(&wc);

    // 创建窗口
    g_hWnd = CreateWindowExA(
        0,
        "HikvisionCameraViewer",
        "Hikvision Camera Viewer",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 660, 280,
        NULL, NULL, hInstance, NULL
    );

    if (g_hWnd == NULL) {
        return 0;
    }

    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);

    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return 0;
}
