#ifndef CAMERAMANAGER_H
#define CAMERAMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QTimer>
#include <QtCore/QMutex>
#include <QtCore/QThread>
#include <QtGui/QPixmap>
#include <QtWidgets/QLabel>

// Hikvision SDK includes (you need to download the SDK)
#ifdef _WIN32
// Note: These headers should be available after installing Hikvision SDK
// If compilation fails, ensure SDK is properly installed in lib directory
extern "C" {
    // Forward declarations to avoid compilation errors when SDK is not available
    // Replace these with actual includes when SDK is installed:
    // #include "HCNetSDK.h"
    // #include "PlayM4.h"

    // Temporary definitions for compilation
    #ifndef HIKVISION_SDK_AVAILABLE
    typedef long LONG;
    typedef unsigned long DWORD;
    typedef unsigned char BYTE;
    typedef struct { int dummy; } NET_DVR_DEVICEINFO_V30;
    typedef struct { int dummy; } NET_DVR_USER_LOGIN_INFO;
    typedef struct { int dummy; } NET_DVR_PREVIEWINFO;
    typedef struct { int dummy; } FRAME_INFO;
    #define NET_DVR_SYSHEAD 1
    #define NET_DVR_STREAMDATA 2
    #define T_YV12 3
    #define STREAME_REALTIME 0
    #define NET_DVR_GET_DEVICECFG_V30 100
    #endif
}
#endif

struct CameraCredential
{
    QString username;
    QString password;
    
    CameraCredential() = default;
    CameraCredential(const QString &user, const QString &pass) 
        : username(user), password(pass) {}
    
    bool operator==(const CameraCredential &other) const {
        return username == other.username && password == other.password;
    }
};

class CameraManager : public QObject
{
    Q_OBJECT

public:
    explicit CameraManager(QObject *parent = nullptr);
    ~CameraManager();

    bool initialize();
    void cleanup();
    
    bool connectToCamera(const QString &ipAddress, const CameraCredential &credential);
    void disconnectFromCamera();
    
    bool isConnected() const { return m_isConnected; }
    QString getLastError() const { return m_lastError; }
    
    void setVideoDisplayWidget(QLabel *widget);

signals:
    void connectionStatusChanged(bool connected);
    void errorOccurred(const QString &error);
    void frameReceived();

private slots:
    void checkConnection();

private:
    static void CALLBACK RealDataCallBack(LONG lRealHandle, DWORD dwDataType, 
                                         BYTE *pBuffer, DWORD dwBufSize, void *pUser);
    static void CALLBACK DecCallBack(LONG nPort, char *pBuf, LONG nSize, 
                                    FRAME_INFO *pFrameInfo, LONG nUser, LONG nReserved2);
    
    void processVideoFrame(BYTE *pBuffer, DWORD dwBufSize);
    bool setupPlayback();
    void cleanupPlayback();

    // Hikvision SDK handles
#ifdef _WIN32
    LONG m_userID;
    LONG m_realPlayHandle;
    LONG m_playPort;
#endif
    
    // State
    bool m_isInitialized;
    bool m_isConnected;
    QString m_lastError;
    QString m_currentIpAddress;
    CameraCredential m_currentCredential;
    
    // Video display
    QLabel *m_videoWidget;
    QMutex m_frameMutex;
    
    // Connection monitoring
    QTimer *m_connectionTimer;
    
    // Constants
    static const int CONNECTION_TIMEOUT = 5000; // 5 seconds
    static const int RECONNECT_INTERVAL = 3000; // 3 seconds
};

#endif // CAMERAMANAGER_H
