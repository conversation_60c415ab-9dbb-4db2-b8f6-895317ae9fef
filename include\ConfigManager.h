#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QSettings>
#include <QtCore/QStringList>
#include <QList>
#include "CameraManager.h"

class ConfigManager : public QObject
{
    Q_OBJECT

public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // IP Address management
    void saveLastIpAddress(const QString &ipAddress);
    QString getLastIpAddress() const;
    
    void addRecentIpAddress(const QString &ipAddress);
    QStringList getRecentIpAddresses() const;
    void clearRecentIpAddresses();
    
    // Credentials management
    void addCredential(const CameraCredential &credential);
    void removeCredential(const CameraCredential &credential);
    QList<CameraCredential> getCredentials() const;
    void clearCredentials();
    
    // Window settings
    void saveWindowGeometry(const QByteArray &geometry);
    QByteArray getWindowGeometry() const;
    
    void saveWindowState(const QByteArray &state);
    QByteArray getWindowState() const;
    
    void saveSplitterState(const QByteArray &state);
    QByteArray getSplitterState() const;
    
    // Application settings
    void saveAutoConnect(bool autoConnect);
    bool getAutoConnect() const;
    
    void saveReconnectAttempts(int attempts);
    int getReconnectAttempts() const;

signals:
    void credentialsChanged();

private:
    void initializeDefaultCredentials();
    QString encryptPassword(const QString &password) const;
    QString decryptPassword(const QString &encryptedPassword) const;
    
    QSettings *m_settings;
    
    // Default credentials
    static const QList<CameraCredential> DEFAULT_CREDENTIALS;
    
    // Settings keys
    static const QString KEY_LAST_IP;
    static const QString KEY_RECENT_IPS;
    static const QString KEY_CREDENTIALS;
    static const QString KEY_WINDOW_GEOMETRY;
    static const QString KEY_WINDOW_STATE;
    static const QString KEY_SPLITTER_STATE;
    static const QString KEY_AUTO_CONNECT;
    static const QString KEY_RECONNECT_ATTEMPTS;
    
    // Limits
    static const int MAX_RECENT_IPS = 10;
    static const int MAX_CREDENTIALS = 20;
};

#endif // CONFIGMANAGER_H
