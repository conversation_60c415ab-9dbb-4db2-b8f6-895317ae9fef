#include <QtWidgets/QApplication>
#include <QtCore/QDir>
#include <QtCore/QStandardPaths>
#include <QtWidgets/QMessageBox>
#include <QtCore/QLoggingCategory>
#include "MainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("Hikvision Camera Viewer");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("CameraViewer");
    app.setOrganizationDomain("cameraviewer.local");
    
    // Set application icon (you can add an icon file later)
    // app.setWindowIcon(QIcon(":/icons/camera.ico"));
    
    // Enable high DPI support
    app.setAttribute(Qt::AA_EnableHighDpiScaling);
    app.setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // Create application data directory if it doesn't exist
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDataDir(appDataPath);
    if (!appDataDir.exists()) {
        appDataDir.mkpath(".");
    }
    
    try {
        // Create and show main window
        MainWindow window;
        window.show();
        
        return app.exec();
    }
    catch (const std::exception &e) {
        QMessageBox::critical(nullptr, "Fatal Error", 
                             QString("An unexpected error occurred:\n%1").arg(e.what()));
        return -1;
    }
    catch (...) {
        QMessageBox::critical(nullptr, "Fatal Error", 
                             "An unknown error occurred during application startup.");
        return -1;
    }
}
