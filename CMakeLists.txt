cmake_minimum_required(VERSION 3.16)
project(HikvisionViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/CameraManager.cpp
    src/ConfigManager.cpp
)

# Header files
set(HEADERS
    include/MainWindow.h
    include/CameraManager.h
    include/ConfigManager.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} 
    Qt6::Core 
    Qt6::Widgets 
    Qt6::Network
)

# Platform specific settings
if(WIN32)
    # Windows specific settings
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )

    # Check if Hikvision SDK libraries exist
    set(HIKVISION_LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    set(HIKVISION_DLL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/lib/dll)

    if(EXISTS ${HIKVISION_LIB_DIR}/HCNetSDK.lib AND EXISTS ${HIKVISION_LIB_DIR}/PlayCtrl.lib)
        message(STATUS "Hikvision SDK libraries found")
        target_compile_definitions(${PROJECT_NAME} PRIVATE HIKVISION_SDK_AVAILABLE)

        # Add Hikvision SDK libraries
        target_link_libraries(${PROJECT_NAME}
            ${HIKVISION_LIB_DIR}/HCNetSDK.lib
            ${HIKVISION_LIB_DIR}/PlayCtrl.lib
        )

        # Copy DLLs to output directory if they exist
        if(EXISTS ${HIKVISION_DLL_DIR})
            add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_directory
                ${HIKVISION_DLL_DIR}
                $<TARGET_FILE_DIR:${PROJECT_NAME}>
                COMMENT "Copying Hikvision SDK DLLs"
            )
        endif()
    else()
        message(WARNING "Hikvision SDK libraries not found in ${HIKVISION_LIB_DIR}")
        message(WARNING "Please download and install Hikvision SDK according to SDK_SETUP.md")
        message(WARNING "The application will compile but camera functionality will be limited")
    endif()
endif()

# Create directories for build
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib/dll)
