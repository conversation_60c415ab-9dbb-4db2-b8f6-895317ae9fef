cmake_minimum_required(VERSION 3.16)
project(DirectCameraViewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 创建直接监控版摄像头查看器
add_executable(direct_camera_viewer WIN32 simple_direct_viewer.cpp)

# Windows特定设置
if(WIN32)
    # 链接Windows API库
    target_link_libraries(direct_camera_viewer 
        user32 
        gdi32 
        wininet
        ws2_32
    )
    
    set_target_properties(direct_camera_viewer PROPERTIES
        WIN32_EXECUTABLE TRUE  # Windows GUI程序
    )
endif()

message(STATUS "Direct Camera Viewer configured successfully!")
message(STATUS "This version automatically connects and displays camera feed")
