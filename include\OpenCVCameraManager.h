#ifndef OPENCVCAMERAMANAGER_H
#define OPENCVCAMERAMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QTimer>
#include <QtCore/QMutex>
#include <QtCore/QThread>
#include <QtGui/QPixmap>
#include <QtWidgets/QLabel>
#include <memory>

// OpenCV includes
#ifdef OPENCV_AVAILABLE
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#endif

struct CameraCredential
{
    QString username;
    QString password;
    
    CameraCredential() = default;
    CameraCredential(const QString &user, const QString &pass) 
        : username(user), password(pass) {}
    
    bool operator==(const CameraCredential &other) const {
        return username == other.username && password == other.password;
    }
};

class VideoCapture;

class OpenCVCameraManager : public QObject
{
    Q_OBJECT

public:
    explicit OpenCVCameraManager(QObject *parent = nullptr);
    ~OpenCVCameraManager();

    bool initialize();
    void cleanup();
    
    bool connectToCamera(const QString &rtspUrl);
    bool connectToCamera(const QString &ipAddress, const CameraCredential &credential);
    void disconnectFromCamera();
    
    bool isConnected() const { return m_isConnected; }
    QString getLastError() const { return m_lastError; }
    
    void setVideoDisplayWidget(QLabel *widget);
    
    // RTSP URL generation
    static QString generateRtspUrl(const QString &ipAddress, const CameraCredential &credential, 
                                  int channel = 101, int port = 554);

signals:
    void connectionStatusChanged(bool connected);
    void errorOccurred(const QString &error);
    void frameReceived();

private slots:
    void captureFrame();
    void checkConnection();

private:
    void startCapture();
    void stopCapture();
    QPixmap matToQPixmap(const cv::Mat &mat);
    
#ifdef OPENCV_AVAILABLE
    std::unique_ptr<cv::VideoCapture> m_capture;
    cv::Mat m_currentFrame;
#endif
    
    // State
    bool m_isInitialized;
    bool m_isConnected;
    QString m_lastError;
    QString m_currentRtspUrl;
    
    // Video display
    QLabel *m_videoWidget;
    QMutex m_frameMutex;
    
    // Timers
    QTimer *m_captureTimer;
    QTimer *m_connectionTimer;
    
    // Settings
    int m_frameRate;
    int m_reconnectInterval;
    int m_connectionTimeout;
    
    // Constants
    static const int DEFAULT_FRAME_RATE = 25; // FPS
    static const int DEFAULT_RECONNECT_INTERVAL = 5000; // 5 seconds
    static const int DEFAULT_CONNECTION_TIMEOUT = 10000; // 10 seconds
    static const int DEFAULT_RTSP_PORT = 554;
};

#endif // OPENCVCAMERAMANAGER_H
