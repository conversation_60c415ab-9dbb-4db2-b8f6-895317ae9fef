@echo off
echo Building Hikvision Camera Viewer...
echo.

REM Check if build directory exists
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

REM Navigate to build directory
cd build

REM Configure with CMake
echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Executable location: build\Release\HikvisionViewer.exe
echo.

REM Check if lib directory exists and copy DLLs
if exist "..\lib\dll" (
    echo Copying DLL files...
    xcopy "..\lib\dll\*" "Release\" /Y /Q
    if %ERRORLEVEL% neq 0 (
        echo Warning: Failed to copy some DLL files
    ) else (
        echo DLL files copied successfully
    )
) else (
    echo Warning: lib\dll directory not found. Please ensure Hikvision SDK DLLs are available.
)

echo.
echo Build process completed!
pause
