# 快速开始指南

由于海康威视SDK获取困难，我们提供了两种解决方案：

## 🚀 推荐方案：使用OpenCV（简单易用）

### 1. 安装OpenCV

#### 方法A：使用vcpkg（推荐）
```bash
# 克隆vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装OpenCV
.\vcpkg install opencv[contrib]:x64-windows

# 集成到Visual Studio
.\vcpkg integrate install
```

#### 方法B：下载预编译版本
1. 访问 https://opencv.org/releases/
2. 下载Windows版本
3. 解压到 `C:\opencv`
4. 添加 `C:\opencv\build\x64\vc15\bin` 到系统PATH

### 2. 构建项目
```bash
# 使用OpenCV构建脚本
build_opencv.bat
```

### 3. 使用方法

#### 输入RTSP地址
在"IP Address"框中输入完整的RTSP地址：
```
rtsp://admin:fssz2016@*************:554/Streaming/Channels/101
rtsp://admin:Fssz123456@*************:554/Streaming/Channels/101
```

#### 或者使用IP地址+凭据
1. 输入IP地址：`*************`
2. 选择预设的用户名/密码组合
3. 程序会自动生成RTSP地址

## 🔧 备选方案：使用海康威视SDK

### 1. 获取SDK文件

#### 官方渠道
- 海康威视开放平台：https://open.hikvision.com/
- 注册账户后下载"设备网络SDK"

#### 其他方式
- 联系设备供应商
- 拨打技术支持：400-700-5998
- GitHub搜索（仅供学习）

### 2. 安装SDK
1. 创建 `lib` 目录
2. 复制 `HCNetSDK.lib` 和 `PlayCtrl.lib` 到 `lib/`
3. 复制所有DLL文件到 `lib/dll/`

### 3. 构建项目
```bash
# 使用原始构建脚本
build.bat
```

## 📋 常见RTSP地址格式

### 海康威视
```
# 主码流
rtsp://用户名:密码@IP:554/Streaming/Channels/101

# 子码流
rtsp://用户名:密码@IP:554/Streaming/Channels/102

# 旧版本格式
rtsp://用户名:密码@IP:554/h264/ch1/main/av_stream
```

### 大华
```
rtsp://用户名:密码@IP:554/cam/realmonitor?channel=1&subtype=0
```

### 宇视
```
rtsp://用户名:密码@IP:554/video1
```

## 🔍 测试连接

### 使用VLC播放器测试
1. 打开VLC播放器
2. 选择"媒体" -> "打开网络串流"
3. 输入RTSP地址测试

### 使用FFplay测试
```bash
ffplay rtsp://admin:fssz2016@*************:554/Streaming/Channels/101
```

## ⚠️ 故障排除

### 1. 编译错误
```
OpenCV not found
```
**解决方案**：
- 确保OpenCV正确安装
- 检查环境变量PATH
- 使用vcpkg安装OpenCV

### 2. 连接失败
```
Failed to open RTSP stream
```
**解决方案**：
- 检查RTSP地址格式
- 确认用户名密码正确
- 检查网络连接
- 尝试使用VLC测试

### 3. 无视频显示
```
Connected but no video
```
**解决方案**：
- 尝试不同的码流（101/102）
- 检查摄像头是否支持RTSP
- 确认摄像头正常工作

## 🎯 推荐配置

### 开发环境
- Windows 10/11 64位
- Visual Studio 2019/2022
- Qt 6.2+
- OpenCV 4.5+

### 网络摄像头设置
- 启用RTSP服务
- 设置合适的码流参数
- 确保网络连通性

## 📞 获取帮助

1. **查看日志**：程序会在控制台输出详细日志
2. **检查网络**：使用ping测试摄像头连通性
3. **测试RTSP**：使用VLC等工具验证RTSP地址
4. **查看文档**：参考ALTERNATIVE_SOLUTION.md

---

**建议**：优先使用OpenCV方案，它更容易部署且支持多种品牌的网络摄像头。
